#!/usr/bin/env python3
"""
Fix and test the prediction feature mismatch issue.
"""
import sys
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.connection import initialize_connection_pool, close_connection_pool
from ml import FootballPredictor, ModelManager
from utils.logger import setup_logging

logger = logging.getLogger(__name__)


def diagnose_feature_mismatch():
    """Diagnose the feature mismatch between training and prediction."""
    try:
        logger.info("🔍 Diagnosing feature mismatch issue...")
        
        # Load model and check features
        model_manager = ModelManager()
        if not model_manager.load_model():
            logger.error("❌ Could not load model for diagnosis")
            return False
        
        logger.info(f"✅ Model loaded successfully")
        logger.info(f"📊 Model expects {len(model_manager.feature_columns)} features:")
        
        # Group features by type for better understanding
        basic_features = []
        derived_features = []
        form_features = []
        
        for feature in model_manager.feature_columns:
            if 'form' in feature:
                form_features.append(feature)
            elif feature in ['home_team_id', 'away_team_id', 'league_id', 'season', 
                           'home_shots', 'away_shots', 'home_shots_on_target', 'away_shots_on_target',
                           'home_possession', 'away_possession', 'home_corners', 'away_corners',
                           'home_fouls', 'away_fouls', 'home_yellow_cards', 'away_yellow_cards',
                           'home_red_cards', 'away_red_cards']:
                basic_features.append(feature)
            else:
                derived_features.append(feature)
        
        logger.info(f"   📋 Basic features ({len(basic_features)}): {basic_features}")
        logger.info(f"   🔧 Derived features ({len(derived_features)}): {derived_features}")
        logger.info(f"   🔥 Form features ({len(form_features)}): {form_features}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error diagnosing feature mismatch: {e}")
        return False


def test_prediction_features():
    """Test if the predictor can now create all required features."""
    try:
        logger.info("🧪 Testing prediction feature creation...")
        
        # Initialize predictor
        predictor = FootballPredictor()
        if not predictor.load_model():
            logger.error("❌ Could not load model for testing")
            return False
        
        logger.info("✅ Predictor model loaded successfully")
        
        # Get a test game
        games = predictor.get_upcoming_games_without_predictions()
        if not games:
            logger.warning("⚠️ No upcoming games available for testing")
            # Create a mock game for testing
            test_game = {
                'id': 999999,
                'home_team_id': 1,
                'away_team_id': 2,
                'league_id': 1,
                'season': '2024-25',
                'home_team_name': 'Test Home Team',
                'away_team_name': 'Test Away Team',
                'date': '2024-01-01'
            }
            logger.info("📋 Using mock game for testing")
        else:
            test_game = games[0]
            logger.info(f"📋 Using real game: {test_game['home_team_name']} vs {test_game['away_team_name']}")
        
        # Test feature creation
        logger.info("🔧 Testing feature creation...")
        features_df = predictor.create_prediction_features(test_game)
        
        if features_df is not None:
            logger.info("✅ Feature creation successful!")
            logger.info(f"📊 Created {len(features_df.columns)} features")
            
            # Check if all expected features are present
            expected_features = predictor.model_manager.feature_columns
            created_features = list(features_df.columns)
            
            missing_features = set(expected_features) - set(created_features)
            extra_features = set(created_features) - set(expected_features)
            
            if not missing_features and not extra_features:
                logger.info("✅ Perfect feature match! All expected features created.")
                return True
            else:
                if missing_features:
                    logger.warning(f"⚠️ Missing features: {list(missing_features)}")
                if extra_features:
                    logger.warning(f"⚠️ Extra features: {list(extra_features)}")
                return len(missing_features) == 0  # Success if no missing features
        else:
            logger.error("❌ Feature creation failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing prediction features: {e}")
        import traceback
        logger.error(f"📋 Full traceback: {traceback.format_exc()}")
        return False


def test_full_prediction_workflow():
    """Test the complete prediction workflow."""
    try:
        logger.info("🎯 Testing full prediction workflow...")
        
        # Initialize predictor
        predictor = FootballPredictor()
        if not predictor.load_model():
            logger.error("❌ Could not load model for workflow test")
            return False
        
        # Get upcoming games
        games = predictor.get_upcoming_games_without_predictions()
        if not games:
            logger.warning("⚠️ No upcoming games available for workflow test")
            return True  # Not an error, just no games to predict
        
        logger.info(f"📊 Found {len(games)} games for prediction testing")
        
        # Test prediction on first game
        test_game = games[0]
        logger.info(f"🎯 Testing prediction for: {test_game['home_team_name']} vs {test_game['away_team_name']}")
        
        prediction = predictor.predict_match(test_game)
        
        if prediction:
            logger.info("✅ Prediction successful!")
            logger.info(f"📊 Prediction details:")
            logger.info(f"   • Outcome: {prediction['predicted_outcome']}")
            logger.info(f"   • Score: {prediction['predicted_home_goals']:.1f} - {prediction['predicted_away_goals']:.1f}")
            logger.info(f"   • Confidence: H:{prediction['confidence_home_win']:.2f} D:{prediction['confidence_draw']:.2f} A:{prediction['confidence_away_win']:.2f}")
            
            # Test saving prediction (optional)
            logger.info("💾 Testing prediction save...")
            if predictor.save_prediction(prediction):
                logger.info("✅ Prediction saved successfully!")
            else:
                logger.warning("⚠️ Prediction save failed (but prediction creation worked)")
            
            return True
        else:
            logger.error("❌ Prediction failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing full prediction workflow: {e}")
        import traceback
        logger.error(f"📋 Full traceback: {traceback.format_exc()}")
        return False


def main():
    """Main function to fix and test prediction features."""
    print("🔧 Prediction Feature Fix & Test")
    print("=" * 50)
    
    try:
        # Setup
        setup_logging()
        initialize_connection_pool()
        
        # Run diagnostics and tests
        success_count = 0
        total_tests = 3
        
        # Test 1: Diagnose feature mismatch
        if diagnose_feature_mismatch():
            success_count += 1
            print("✅ Test 1 PASSED: Feature mismatch diagnosed")
        else:
            print("❌ Test 1 FAILED: Could not diagnose feature mismatch")
        
        # Test 2: Test feature creation
        if test_prediction_features():
            success_count += 1
            print("✅ Test 2 PASSED: Prediction features created successfully")
        else:
            print("❌ Test 2 FAILED: Prediction feature creation failed")
        
        # Test 3: Test full workflow
        if test_full_prediction_workflow():
            success_count += 1
            print("✅ Test 3 PASSED: Full prediction workflow successful")
        else:
            print("❌ Test 3 FAILED: Full prediction workflow failed")
        
        # Summary
        print(f"\n📊 Test Results: {success_count}/{total_tests} tests passed")
        
        if success_count == total_tests:
            print("\n🎉 SUCCESS: All tests passed!")
            print("✅ Feature mismatch issue has been resolved")
            print("✅ Prediction system is working correctly")
            print("\n🚀 Ready for production predictions!")
        elif success_count >= 2:
            print("\n🟡 PARTIAL SUCCESS: Most tests passed")
            print("⚠️ Some minor issues remain but core functionality works")
        else:
            print("\n❌ FAILURE: Major issues remain")
            print("🔧 Additional debugging required")
        
        return success_count == total_tests
        
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        close_connection_pool()


if __name__ == "__main__":
    main()
