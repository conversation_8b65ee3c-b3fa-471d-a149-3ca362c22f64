"""
Model management for training and storing football prediction models.
"""
import logging
import pickle
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import lightgbm as lgb

from database import get_db_cursor
from .intelligent_data_handler import IntelligentDataHandler
from database.models import Match, Team, League

logger = logging.getLogger(__name__)


class ModelManager:
    """Manages training, storing, and loading of prediction models."""

    def __init__(self):
        """Initialize model manager."""
        self.models = {}
        self.scalers = {}
        self.feature_columns = []
        self.data_handler = IntelligentDataHandler()

    def check_existing_model(self, model_name: str = "football_predictor") -> bool:
        """
        Check if a model already exists in the database.

        Args:
            model_name: Name of the model to check

        Returns:
            True if model exists, False otherwise
        """
        try:
            with get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*) as count FROM models
                    WHERE name = %s AND is_active = TRUE
                """, (model_name,))
                result = cursor.fetchone()
                return result['count'] > 0
        except Exception as e:
            logger.error(f"Error checking existing model: {e}")
            return False

    def get_training_data(self) -> Optional[pd.DataFrame]:
        """
        Get training data from the database.

        Returns:
            DataFrame with training data or None if insufficient data
        """
        try:
            with get_db_cursor() as cursor:
                # Get matches with complete data
                cursor.execute("""
                    SELECT
                        m.id,
                        m.home_team_id,
                        m.away_team_id,
                        m.home_goals,
                        m.away_goals,
                        m.league_id,
                        m.season,
                        m.date,
                        m.home_shots,
                        m.away_shots,
                        m.home_shots_on_target,
                        m.away_shots_on_target,
                        m.home_possession,
                        m.away_possession,
                        m.home_corners,
                        m.away_corners,
                        m.home_fouls,
                        m.away_fouls,
                        m.home_yellow_cards,
                        m.away_yellow_cards,
                        m.home_red_cards,
                        m.away_red_cards,
                        ht.name as home_team_name,
                        at.name as away_team_name,
                        l.name as league_name,
                        l.country as league_country
                    FROM matches m
                    JOIN teams ht ON m.home_team_id = ht.id
                    JOIN teams at ON m.away_team_id = at.id
                    JOIN leagues l ON m.league_id = l.id AND m.season = l.season
                    WHERE m.home_goals IS NOT NULL
                    AND m.away_goals IS NOT NULL
                    AND m.status IN ('FT', 'AET', 'PEN')
                    ORDER BY m.date DESC
                """)

                matches = cursor.fetchall()

            if len(matches) < 50:
                logger.warning(f"Insufficient training data: only {len(matches)} matches available")
                return None

            df = pd.DataFrame(matches)
            logger.info(f"Retrieved {len(df)} matches for training")
            return df

        except Exception as e:
            logger.error(f"Error getting training data: {e}")
            return None

    def create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create features for machine learning.

        Args:
            df: Raw match data

        Returns:
            DataFrame with engineered features
        """
        try:
            # Create target variables
            df['outcome'] = df.apply(lambda row:
                1 if row['home_goals'] > row['away_goals'] else
                (0 if row['home_goals'] == row['away_goals'] else 2), axis=1)
            df['total_goals'] = df['home_goals'] + df['away_goals']

            # Basic features
            features_df = pd.DataFrame()
            features_df['home_team_id'] = df['home_team_id']
            features_df['away_team_id'] = df['away_team_id']
            features_df['league_id'] = df['league_id']
            features_df['season'] = df['season']

            # Copy statistical features (will be filled intelligently later)
            stat_columns = [
                'home_shots', 'away_shots', 'home_shots_on_target', 'away_shots_on_target',
                'home_possession', 'away_possession', 'home_corners', 'away_corners',
                'home_fouls', 'away_fouls', 'home_yellow_cards', 'away_yellow_cards',
                'home_red_cards', 'away_red_cards'
            ]

            for col in stat_columns:
                if col in df.columns:
                    features_df[col] = df[col]
                else:
                    # Column doesn't exist in data - create with NaN
                    features_df[col] = np.nan

            # Apply intelligent data handling FIRST (before calculating derived features)
            logger.info("🧠 Applying intelligent data handling...")
            features_df = self.data_handler.fill_missing_data_intelligently(features_df, df)

            # Create alternative features for teams/leagues with missing detailed stats
            features_df = self.data_handler.create_alternative_features(features_df, df)

            # NOW calculate derived features from filled data
            logger.info("🔧 Calculating derived features from filled data...")

            # Derived features (now calculated from filled data)
            features_df['shots_ratio'] = features_df['home_shots'] / (features_df['away_shots'] + 1)
            features_df['shots_on_target_ratio'] = features_df['home_shots_on_target'] / (features_df['away_shots_on_target'] + 1)
            features_df['possession_diff'] = features_df['home_possession'] - features_df['away_possession']
            features_df['corners_ratio'] = features_df['home_corners'] / (features_df['away_corners'] + 1)
            features_df['fouls_diff'] = features_df['home_fouls'] - features_df['away_fouls']
            features_df['cards_diff'] = (features_df['home_yellow_cards'] + features_df['home_red_cards'] * 2) - \
                                      (features_df['away_yellow_cards'] + features_df['away_red_cards'] * 2)

            # Advanced derived features for better prediction
            features_df['shot_accuracy_home'] = features_df['home_shots_on_target'] / (features_df['home_shots'] + 1)
            features_df['shot_accuracy_away'] = features_df['away_shots_on_target'] / (features_df['away_shots'] + 1)
            features_df['shot_accuracy_diff'] = features_df['shot_accuracy_home'] - features_df['shot_accuracy_away']

            # Attacking vs Defensive balance
            features_df['home_attack_index'] = (features_df['home_shots'] + features_df['home_corners']) / 2
            features_df['away_attack_index'] = (features_df['away_shots'] + features_df['away_corners']) / 2
            features_df['attack_balance'] = features_df['home_attack_index'] / (features_df['away_attack_index'] + 1)

            # Discipline features
            features_df['home_discipline'] = features_df['home_fouls'] + features_df['home_yellow_cards'] * 2 + features_df['home_red_cards'] * 5
            features_df['away_discipline'] = features_df['away_fouls'] + features_df['away_yellow_cards'] * 2 + features_df['away_red_cards'] * 5
            features_df['discipline_diff'] = features_df['home_discipline'] - features_df['away_discipline']

            # Additional performance enhancement features
            logger.info("🚀 Adding performance enhancement features...")

            # Efficiency ratios
            features_df['home_efficiency'] = features_df['home_shots_on_target'] / (features_df['home_shots'] + 1)
            features_df['away_efficiency'] = features_df['away_shots_on_target'] / (features_df['away_shots'] + 1)
            features_df['efficiency_advantage'] = features_df['home_efficiency'] - features_df['away_efficiency']

            # Pressure indicators
            features_df['home_pressure'] = (features_df['home_shots'] + features_df['home_corners'] * 2) / (features_df['home_fouls'] + 1)
            features_df['away_pressure'] = (features_df['away_shots'] + features_df['away_corners'] * 2) / (features_df['away_fouls'] + 1)
            features_df['pressure_balance'] = features_df['home_pressure'] / (features_df['away_pressure'] + 1)

            # Control metrics
            features_df['home_control'] = features_df['home_possession'] * features_df['home_efficiency']
            features_df['away_control'] = features_df['away_possession'] * features_df['away_efficiency']
            features_df['control_dominance'] = features_df['home_control'] / (features_df['away_control'] + 1)

            # Add team form features (recent performance)
            logger.info("   🔥 Adding team form features...")
            form_features = self._add_team_form_features(df)
            if form_features is not None:
                for col in form_features.columns:
                    if col not in features_df.columns:
                        features_df[col] = form_features[col]
                logger.info(f"   ✅ Added {len(form_features.columns)} team form features")
            else:
                logger.warning("   ⚠️ Could not add team form features - insufficient historical data")

            # Add targets
            features_df['outcome'] = df['outcome']
            features_df['home_goals'] = df['home_goals']
            features_df['away_goals'] = df['away_goals']
            features_df['total_goals'] = df['total_goals']

            # Store feature columns (excluding targets)
            self.feature_columns = [col for col in features_df.columns
                                  if col not in ['outcome', 'home_goals', 'away_goals', 'total_goals']]

            logger.info(f"Created {len(self.feature_columns)} features for training")
            return features_df

        except Exception as e:
            logger.error(f"Error creating features: {e}")
            raise

    def train_model(self, model_name: str = "football_predictor",
                   model_type: str = "xgboost") -> Dict[str, Any]:
        """
        Train a new model or retrain existing model.

        Args:
            model_name: Name of the model
            model_type: Type of model ('xgboost', 'lightgbm', 'random_forest')

        Returns:
            Dictionary with training results
        """
        try:
            logger.info(f"🚀 Starting model training: {model_name} ({model_type})")
            logger.info(f"📊 Training configuration: learning_rate=default, max_depth=6, n_estimators=100")

            # Step 1: Get training data
            logger.info("📥 Step 1: Loading training data from database...")
            df = self.get_training_data()
            if df is None:
                logger.error("❌ Failed to get training data - insufficient matches in database")
                return {'success': False, 'error': 'Insufficient training data (need at least 50 matches)'}

            logger.info(f"✅ Training data loaded: {len(df)} matches")
            logger.info(f"📋 Data columns: {list(df.columns)}")
            logger.info(f"📊 Data shape: {df.shape}")

            # Step 2: Create features
            logger.info("🔧 Step 2: Creating features from match data...")
            try:
                features_df = self.create_features(df)
                logger.info(f"✅ Feature engineering completed")
                logger.info(f"📊 Features shape: {features_df.shape}")
                logger.info(f"🎯 Feature columns: {len(self.feature_columns)} features")
                logger.debug(f"📝 Feature list: {self.feature_columns}")
            except Exception as e:
                logger.error(f"❌ Feature creation failed: {e}")
                return {'success': False, 'error': f'Feature creation failed: {str(e)}'}

            # Step 3: Prepare training data
            logger.info("📊 Step 3: Preparing training data...")
            try:
                X = features_df[self.feature_columns]
                y_outcome = features_df['outcome']
                y_home_goals = features_df['home_goals']
                y_away_goals = features_df['away_goals']
                y_total_goals = features_df['total_goals']

                logger.info(f"✅ Data preparation completed")
                logger.info(f"📊 Features (X) shape: {X.shape}")
                logger.info(f"🎯 Target distributions:")
                logger.info(f"   • Outcomes: {y_outcome.value_counts().to_dict()}")
                logger.info(f"   • Home goals: min={y_home_goals.min()}, max={y_home_goals.max()}, mean={y_home_goals.mean():.2f}")
                logger.info(f"   • Away goals: min={y_away_goals.min()}, max={y_away_goals.max()}, mean={y_away_goals.mean():.2f}")

                # Check for missing values
                missing_features = X.isnull().sum()
                if missing_features.sum() > 0:
                    logger.warning(f"⚠️ Missing values detected: {missing_features[missing_features > 0].to_dict()}")
                else:
                    logger.info("✅ No missing values in features")

            except Exception as e:
                logger.error(f"❌ Data preparation failed: {e}")
                return {'success': False, 'error': f'Data preparation failed: {str(e)}'}

            # Step 4: Split data
            logger.info("✂️ Step 4: Splitting data into train/test sets...")
            try:
                X_train, X_test, y_outcome_train, y_outcome_test = train_test_split(
                    X, y_outcome, test_size=0.2, random_state=42, stratify=y_outcome)

                _, _, y_home_train, y_home_test = train_test_split(
                    X, y_home_goals, test_size=0.2, random_state=42)

                _, _, y_away_train, y_away_test = train_test_split(
                    X, y_away_goals, test_size=0.2, random_state=42)

                logger.info(f"✅ Data split completed")
                logger.info(f"📊 Training set: {X_train.shape[0]} samples")
                logger.info(f"📊 Test set: {X_test.shape[0]} samples")
                logger.info(f"🎯 Training outcome distribution: {y_outcome_train.value_counts().to_dict()}")

            except Exception as e:
                logger.error(f"❌ Data splitting failed: {e}")
                return {'success': False, 'error': f'Data splitting failed: {str(e)}'}

            # Step 5: Scale features
            logger.info("⚖️ Step 5: Scaling features...")
            try:
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)

                logger.info(f"✅ Feature scaling completed")
                logger.info(f"📊 Scaled features shape: {X_train_scaled.shape}")

            except Exception as e:
                logger.error(f"❌ Feature scaling failed: {e}")
                return {'success': False, 'error': f'Feature scaling failed: {str(e)}'}

            # Step 6: Train models
            logger.info(f"🤖 Step 6: Training {model_type} models...")
            models = {}
            accuracies = {}

            try:
                if model_type == "xgboost":
                    logger.info("🚀 Training XGBoost models...")

                    # Outcome classifier
                    logger.info("   📊 Training outcome classifier...")
                    models['outcome'] = xgb.XGBClassifier(
                        n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42,
                        eval_metric='mlogloss', verbosity=0)
                    models['outcome'].fit(X_train_scaled, y_outcome_train)
                    logger.info("   ✅ Outcome classifier trained")

                    # Goal regressors
                    logger.info("   ⚽ Training home goals regressor...")
                    models['home_goals'] = xgb.XGBRegressor(
                        n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42,
                        eval_metric='rmse', verbosity=0)
                    models['home_goals'].fit(X_train_scaled, y_home_train)
                    logger.info("   ✅ Home goals regressor trained")

                    logger.info("   ⚽ Training away goals regressor...")
                    models['away_goals'] = xgb.XGBRegressor(
                        n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42,
                        eval_metric='rmse', verbosity=0)
                    models['away_goals'].fit(X_train_scaled, y_away_train)
                    logger.info("   ✅ Away goals regressor trained")

                elif model_type == "lightgbm":
                    logger.info("🚀 Training LightGBM models...")

                    # Outcome classifier
                    logger.info("   📊 Training outcome classifier...")
                    models['outcome'] = lgb.LGBMClassifier(
                        n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42,
                        verbosity=-1)
                    models['outcome'].fit(X_train_scaled, y_outcome_train)
                    logger.info("   ✅ Outcome classifier trained")

                    # Goal regressors
                    logger.info("   ⚽ Training home goals regressor...")
                    models['home_goals'] = lgb.LGBMRegressor(
                        n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42,
                        verbosity=-1)
                    models['home_goals'].fit(X_train_scaled, y_home_train)
                    logger.info("   ✅ Home goals regressor trained")

                    logger.info("   ⚽ Training away goals regressor...")
                    models['away_goals'] = lgb.LGBMRegressor(
                        n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42,
                        verbosity=-1)
                    models['away_goals'].fit(X_train_scaled, y_away_train)
                    logger.info("   ✅ Away goals regressor trained")

                else:  # random_forest
                    logger.info("🚀 Training Random Forest models...")

                    # Outcome classifier
                    logger.info("   📊 Training outcome classifier...")
                    models['outcome'] = RandomForestClassifier(
                        n_estimators=100, max_depth=10, random_state=42, verbose=0)
                    models['outcome'].fit(X_train_scaled, y_outcome_train)
                    logger.info("   ✅ Outcome classifier trained")

                    # Goal regressors
                    logger.info("   ⚽ Training home goals regressor...")
                    models['home_goals'] = RandomForestRegressor(
                        n_estimators=100, max_depth=10, random_state=42, verbose=0)
                    models['home_goals'].fit(X_train_scaled, y_home_train)
                    logger.info("   ✅ Home goals regressor trained")

                    logger.info("   ⚽ Training away goals regressor...")
                    models['away_goals'] = RandomForestRegressor(
                        n_estimators=100, max_depth=10, random_state=42, verbose=0)
                    models['away_goals'].fit(X_train_scaled, y_away_train)
                    logger.info("   ✅ Away goals regressor trained")

                logger.info(f"✅ All {model_type} models trained successfully")

            except Exception as e:
                logger.error(f"❌ Model training failed: {e}")
                import traceback
                logger.error(f"📋 Full traceback: {traceback.format_exc()}")
                return {'success': False, 'error': f'Model training failed: {str(e)}'}

            # Step 7: Evaluate models
            logger.info("📊 Step 7: Evaluating model performance...")
            try:
                logger.info("   🎯 Making predictions on test set...")
                outcome_pred = models['outcome'].predict(X_test_scaled)
                home_goals_pred = models['home_goals'].predict(X_test_scaled)
                away_goals_pred = models['away_goals'].predict(X_test_scaled)

                logger.info("   📊 Calculating accuracy metrics...")
                accuracies['outcome'] = accuracy_score(y_outcome_test, outcome_pred)
                accuracies['home_goals'] = mean_absolute_error(y_home_test, home_goals_pred)
                accuracies['away_goals'] = mean_absolute_error(y_away_test, away_goals_pred)

                logger.info("✅ Model evaluation completed")
                logger.info(f"📊 Performance metrics:")
                logger.info(f"   • Outcome accuracy: {accuracies['outcome']:.4f}")
                logger.info(f"   • Home goals MAE: {accuracies['home_goals']:.4f}")
                logger.info(f"   • Away goals MAE: {accuracies['away_goals']:.4f}")

                # Additional evaluation metrics
                from sklearn.metrics import classification_report, confusion_matrix
                logger.info("📋 Detailed outcome classification report:")
                class_report = classification_report(y_outcome_test, outcome_pred,
                                                   target_names=['Draw', 'Home Win', 'Away Win'])
                logger.info(f"\n{class_report}")

                conf_matrix = confusion_matrix(y_outcome_test, outcome_pred)
                logger.info(f"📊 Confusion matrix:\n{conf_matrix}")

            except Exception as e:
                logger.error(f"❌ Model evaluation failed: {e}")
                return {'success': False, 'error': f'Model evaluation failed: {str(e)}'}

            # Step 8: Store models
            logger.info("💾 Step 8: Storing trained models...")
            try:
                self.models = models
                self.scalers['features'] = scaler

                # Prepare model data for database storage
                model_data = {
                    'models': models,
                    'scalers': self.scalers,
                    'feature_columns': self.feature_columns
                }

                version = datetime.now().strftime("%Y%m%d_%H%M%S")
                logger.info(f"📝 Model version: {version}")

                saved = self._save_model_to_db(model_name, version, model_type, model_data,
                                             len(X_train), accuracies['outcome'])

                if saved:
                    logger.info("✅ Model saved to database successfully")
                else:
                    logger.warning("⚠️ Model trained but failed to save to database")

            except Exception as e:
                logger.error(f"❌ Model storage failed: {e}")
                return {'success': False, 'error': f'Model storage failed: {str(e)}'}

            # Step 9: Prepare final result
            result = {
                'success': True,
                'model_name': model_name,
                'version': version,
                'model_type': model_type,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'total_features': len(self.feature_columns),
                'accuracies': accuracies,
                'saved_to_db': saved
            }

            logger.info("🎉 Model training completed successfully!")
            logger.info(f"📊 Final summary:")
            logger.info(f"   • Model: {model_name} v{version}")
            logger.info(f"   • Algorithm: {model_type}")
            logger.info(f"   • Training samples: {len(X_train)}")
            logger.info(f"   • Test samples: {len(X_test)}")
            logger.info(f"   • Features: {len(self.feature_columns)}")
            logger.info(f"   • Outcome accuracy: {accuracies['outcome']:.4f}")
            logger.info(f"   • Goals MAE: {(accuracies['home_goals'] + accuracies['away_goals'])/2:.4f}")
            logger.info(f"   • Database saved: {saved}")

            return result

        except Exception as e:
            logger.error(f"❌ Unexpected error during model training: {e}")
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")
            return {'success': False, 'error': f'Unexpected training error: {str(e)}'}

    def _save_model_to_db(self, name: str, version: str, model_type: str,
                         model_data: Dict[str, Any], training_samples: int,
                         validation_accuracy: float) -> bool:
        """
        Save model to database.

        Args:
            name: Model name
            version: Model version
            model_type: Type of model
            model_data: Serialized model data
            training_samples: Number of training samples
            validation_accuracy: Validation accuracy

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            logger.info(f"💾 Saving model to database: {name} v{version}")

            # Serialize model data
            logger.info("   🔄 Serializing model data...")
            serialized_data = pickle.dumps(model_data)
            feature_columns_json = json.dumps(self.feature_columns)

            logger.info(f"   📊 Serialized data size: {len(serialized_data)} bytes")
            logger.info(f"   📋 Feature columns: {len(self.feature_columns)} features")

            with get_db_cursor() as cursor:
                # Deactivate existing models
                logger.info("   🔄 Deactivating existing models...")
                cursor.execute("""
                    UPDATE models SET is_active = FALSE
                    WHERE name = %s
                """, (name,))
                deactivated_count = cursor.rowcount
                logger.info(f"   📊 Deactivated {deactivated_count} existing models")

                # Insert new model
                logger.info("   💾 Inserting new model...")
                cursor.execute("""
                    INSERT INTO models (
                        name, version, model_type, model_data, feature_columns,
                        training_samples, validation_accuracy, is_active
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (name, version, model_type, serialized_data, feature_columns_json,
                     training_samples, validation_accuracy, True))

                logger.info(f"   ✅ Model inserted with ID: {cursor.lastrowid if hasattr(cursor, 'lastrowid') else 'N/A'}")

            logger.info(f"✅ Model saved to database successfully: {name} v{version}")
            logger.info(f"   📊 Training samples: {training_samples}")
            logger.info(f"   🎯 Validation accuracy: {validation_accuracy:.4f}")
            logger.info(f"   🔧 Model type: {model_type}")
            return True

        except Exception as e:
            logger.error(f"❌ Error saving model to database: {e}")
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")
            return False

    def load_model(self, model_name: str = "football_predictor") -> bool:
        """
        Load model from database.

        Args:
            model_name: Name of the model to load

        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            logger.info(f"📥 Loading model from database: {model_name}")

            with get_db_cursor() as cursor:
                logger.info("   🔍 Searching for active model...")
                cursor.execute("""
                    SELECT model_data, feature_columns, version, model_type,
                           training_samples, validation_accuracy, created_at
                    FROM models
                    WHERE name = %s AND is_active = TRUE
                    ORDER BY created_at DESC LIMIT 1
                """, (model_name,))

                result = cursor.fetchone()

            if not result:
                logger.warning(f"❌ No active model found: {model_name}")
                logger.info("   💡 Tip: Train a model first using 'Model Management' → 'Model Training'")
                return False

            logger.info(f"   ✅ Found active model: {model_name}")
            logger.info(f"   📊 Model details:")
            logger.info(f"      • Version: {result['version']}")
            logger.info(f"      • Type: {result['model_type']}")
            logger.info(f"      • Training samples: {result['training_samples']}")
            logger.info(f"      • Validation accuracy: {result['validation_accuracy']:.4f}")
            logger.info(f"      • Created: {result['created_at']}")

            # Deserialize model data
            logger.info("   🔄 Deserializing model data...")
            try:
                model_data = pickle.loads(result['model_data'])
                self.models = model_data['models']
                self.scalers = model_data['scalers']
                self.feature_columns = json.loads(result['feature_columns'])

                logger.info(f"   ✅ Model deserialization completed")
                logger.info(f"   📊 Loaded components:")
                logger.info(f"      • Models: {list(self.models.keys()) if self.models else 'None'}")
                logger.info(f"      • Scalers: {list(self.scalers.keys()) if self.scalers else 'None'}")
                logger.info(f"      • Features: {len(self.feature_columns)} columns")

            except Exception as e:
                logger.error(f"   ❌ Model deserialization failed: {e}")
                return False

            logger.info(f"✅ Model loaded successfully: {model_name}")
            return True

        except Exception as e:
            logger.error(f"❌ Error loading model: {e}")
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")
            return False

    def get_model_info(self, model_name: str = "football_predictor") -> Optional[Dict[str, Any]]:
        """
        Get information about a model.

        Args:
            model_name: Name of the model

        Returns:
            Dictionary with model information or None if not found
        """
        try:
            with get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT name, version, model_type, training_date,
                           training_samples, validation_accuracy, is_active
                    FROM models
                    WHERE name = %s AND is_active = TRUE
                    ORDER BY created_at DESC LIMIT 1
                """, (model_name,))

                result = cursor.fetchone()

            if result:
                return dict(result)
            return None

        except Exception as e:
            logger.error(f"Error getting model info: {e}")
            return None

    def list_models(self) -> List[Dict[str, Any]]:
        """
        List all models in the database.

        Returns:
            List of model information dictionaries
        """
        try:
            with get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT name, version, model_type, training_date,
                           training_samples, validation_accuracy, is_active
                    FROM models
                    ORDER BY training_date DESC
                """)

                results = cursor.fetchall()

            return [dict(result) for result in results]

        except Exception as e:
            logger.error(f"Error listing models: {e}")
            return []

    def _add_team_form_features(self, df: pd.DataFrame, form_games: int = 5) -> Optional[pd.DataFrame]:
        """
        Add team form features based on recent performance.

        Args:
            df: Match data DataFrame
            form_games: Number of recent games to consider for form

        Returns:
            DataFrame with team form features or None if insufficient data
        """
        try:
            form_features = pd.DataFrame(index=df.index)

            # Sort by date to ensure chronological order
            df_sorted = df.sort_values('date').reset_index(drop=True)

            for idx, match in df_sorted.iterrows():
                home_team_id = match['home_team_id']
                away_team_id = match['away_team_id']
                match_date = match['date']

                # Get recent form for home team
                home_form = self._get_team_form(df_sorted, home_team_id, match_date, form_games)

                # Get recent form for away team
                away_form = self._get_team_form(df_sorted, away_team_id, match_date, form_games)

                # Add form features
                form_features.loc[idx, 'home_form_points'] = home_form.get('points', 0)
                form_features.loc[idx, 'away_form_points'] = away_form.get('points', 0)
                form_features.loc[idx, 'home_form_goals_scored'] = home_form.get('goals_scored', 0)
                form_features.loc[idx, 'away_form_goals_scored'] = away_form.get('goals_scored', 0)
                form_features.loc[idx, 'home_form_goals_conceded'] = home_form.get('goals_conceded', 0)
                form_features.loc[idx, 'away_form_goals_conceded'] = away_form.get('goals_conceded', 0)
                form_features.loc[idx, 'home_form_wins'] = home_form.get('wins', 0)
                form_features.loc[idx, 'away_form_wins'] = away_form.get('wins', 0)

            # Calculate derived form features
            form_features['form_points_diff'] = form_features['home_form_points'] - form_features['away_form_points']
            form_features['form_goals_diff'] = (form_features['home_form_goals_scored'] - form_features['home_form_goals_conceded']) - \
                                             (form_features['away_form_goals_scored'] - form_features['away_form_goals_conceded'])
            form_features['form_wins_diff'] = form_features['home_form_wins'] - form_features['away_form_wins']

            # Fill any remaining NaN values with 0
            form_features = form_features.fillna(0)

            return form_features

        except Exception as e:
            logger.error(f"Error adding team form features: {e}")
            return None

    def _get_team_form(self, df: pd.DataFrame, team_id: int, match_date, num_games: int = 5) -> Dict[str, float]:
        """
        Get recent form statistics for a team.

        Args:
            df: Match data DataFrame
            team_id: Team ID
            match_date: Current match date
            num_games: Number of recent games to consider

        Returns:
            Dictionary with form statistics
        """
        try:
            # Get recent matches for this team before the current match date
            recent_matches = df[
                ((df['home_team_id'] == team_id) | (df['away_team_id'] == team_id)) &
                (df['date'] < match_date) &
                (df['home_goals'].notna()) &
                (df['away_goals'].notna())
            ].tail(num_games)

            if len(recent_matches) == 0:
                return {'points': 0, 'goals_scored': 0, 'goals_conceded': 0, 'wins': 0}

            points = 0
            goals_scored = 0
            goals_conceded = 0
            wins = 0

            for _, match in recent_matches.iterrows():
                if match['home_team_id'] == team_id:
                    # Team played at home
                    team_goals = match['home_goals']
                    opponent_goals = match['away_goals']
                else:
                    # Team played away
                    team_goals = match['away_goals']
                    opponent_goals = match['home_goals']

                goals_scored += team_goals
                goals_conceded += opponent_goals

                # Calculate points (3 for win, 1 for draw, 0 for loss)
                if team_goals > opponent_goals:
                    points += 3
                    wins += 1
                elif team_goals == opponent_goals:
                    points += 1

            # Calculate averages
            num_matches = len(recent_matches)
            return {
                'points': points / num_matches if num_matches > 0 else 0,
                'goals_scored': goals_scored / num_matches if num_matches > 0 else 0,
                'goals_conceded': goals_conceded / num_matches if num_matches > 0 else 0,
                'wins': wins / num_matches if num_matches > 0 else 0
            }

        except Exception as e:
            logger.error(f"Error calculating team form: {e}")
            return {'points': 0, 'goals_scored': 0, 'goals_conceded': 0, 'wins': 0}

    def train_model_with_draw_optimization(self, model_name: str = "football_predictor",
                                         model_type: str = "xgboost") -> Dict[str, Any]:
        """
        Train model with specific optimizations for draw predictions.

        Args:
            model_name: Name of the model
            model_type: Type of model ('xgboost', 'lightgbm', 'random_forest')

        Returns:
            Dictionary with training results
        """
        try:
            logger.info(f"🎯 Starting draw-optimized model training: {model_name} ({model_type})")
            logger.info("📊 Draw optimization features:")
            logger.info("   • Class balancing: Draw weight = 3.0")
            logger.info("   • Enhanced draw-specific features")
            logger.info("   • Optimized hyperparameters for balanced predictions")

            # Get training data
            logger.info("📥 Step 1: Loading training data...")
            df = self.get_training_data()
            if df is None:
                logger.error("❌ Failed to get training data")
                return {'success': False, 'error': 'Insufficient training data'}

            logger.info(f"✅ Training data loaded: {len(df)} matches")

            # Create enhanced features with draw-specific additions
            logger.info("🔧 Step 2: Creating enhanced features with draw optimizations...")
            features_df = self.create_features(df)

            # Add draw-specific features
            draw_features = self._add_draw_specific_features(df, features_df)
            if draw_features is not None:
                for col in draw_features.columns:
                    if col not in features_df.columns:
                        features_df[col] = draw_features[col]
                logger.info(f"✅ Added {len(draw_features.columns)} draw-specific features")

            # Prepare data with class balancing
            logger.info("📊 Step 3: Preparing data with draw optimization...")
            X = features_df[self.feature_columns]
            y_outcome = features_df['outcome']
            y_home_goals = features_df['home_goals']
            y_away_goals = features_df['away_goals']

            # Check class distribution
            class_dist = y_outcome.value_counts().to_dict()
            logger.info(f"🎯 Class distribution: {class_dist}")
            draw_percentage = class_dist.get(0, 0) / len(y_outcome) * 100
            logger.info(f"📊 Draw percentage: {draw_percentage:.1f}%")

            # Split data
            X_train, X_test, y_outcome_train, y_outcome_test = train_test_split(
                X, y_outcome, test_size=0.2, random_state=42, stratify=y_outcome)

            _, _, y_home_train, y_home_test = train_test_split(
                X, y_home_goals, test_size=0.2, random_state=42)

            _, _, y_away_train, y_away_test = train_test_split(
                X, y_away_goals, test_size=0.2, random_state=42)

            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)

            logger.info(f"✅ Data preparation completed")
            logger.info(f"📊 Training set: {X_train.shape[0]} samples")
            logger.info(f"📊 Test set: {X_test.shape[0]} samples")

            # Train models with draw optimization
            logger.info("🤖 Step 4: Training draw-optimized models...")
            models = {}

            if model_type == "xgboost":
                # Calculate class weights for draw optimization
                from sklearn.utils.class_weight import compute_class_weight
                classes = np.unique(y_outcome_train)
                class_weights = compute_class_weight('balanced', classes=classes, y=y_outcome_train)

                # Boost draw class specifically
                sample_weights = np.ones(len(y_outcome_train))
                draw_indices = y_outcome_train == 0  # Draw class
                sample_weights[draw_indices] *= 3.0  # Triple weight for draws

                logger.info("   🎯 Training draw-optimized outcome classifier...")
                models['outcome'] = xgb.XGBClassifier(
                    n_estimators=200,           # More trees for stability
                    max_depth=6,                # Prevent overfitting
                    learning_rate=0.05,         # Slower learning
                    subsample=0.8,              # Row sampling
                    colsample_bytree=0.8,       # Feature sampling
                    reg_alpha=0.1,              # L1 regularization
                    reg_lambda=1.0,             # L2 regularization
                    eval_metric='mlogloss',
                    verbosity=0,
                    random_state=42
                )
                models['outcome'].fit(X_train_scaled, y_outcome_train, sample_weight=sample_weights)
                logger.info("   ✅ Draw-optimized outcome classifier trained")

                # Goal regressors (unchanged)
                logger.info("   ⚽ Training goal regressors...")
                models['home_goals'] = xgb.XGBRegressor(
                    n_estimators=150, max_depth=6, learning_rate=0.05, random_state=42,
                    eval_metric='rmse', verbosity=0)
                models['home_goals'].fit(X_train_scaled, y_home_train)

                models['away_goals'] = xgb.XGBRegressor(
                    n_estimators=150, max_depth=6, learning_rate=0.05, random_state=42,
                    eval_metric='rmse', verbosity=0)
                models['away_goals'].fit(X_train_scaled, y_away_train)
                logger.info("   ✅ Goal regressors trained")

            # Evaluate with focus on draw performance
            logger.info("📊 Step 5: Evaluating draw-optimized performance...")
            outcome_pred = models['outcome'].predict(X_test_scaled)
            home_goals_pred = models['home_goals'].predict(X_test_scaled)
            away_goals_pred = models['away_goals'].predict(X_test_scaled)

            # Calculate metrics
            from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

            accuracies = {
                'outcome': accuracy_score(y_outcome_test, outcome_pred),
                'home_goals': mean_absolute_error(y_home_test, home_goals_pred),
                'away_goals': mean_absolute_error(y_away_test, away_goals_pred)
            }

            # Detailed classification report
            class_report = classification_report(y_outcome_test, outcome_pred,
                                               target_names=['Draw', 'Home Win', 'Away Win'],
                                               output_dict=True)

            conf_matrix = confusion_matrix(y_outcome_test, outcome_pred)

            logger.info("✅ Draw-optimized evaluation completed")
            logger.info(f"📊 Overall accuracy: {accuracies['outcome']:.4f}")
            logger.info(f"🎯 Draw performance:")
            logger.info(f"   • Precision: {class_report['Draw']['precision']:.3f}")
            logger.info(f"   • Recall: {class_report['Draw']['recall']:.3f}")
            logger.info(f"   • F1-score: {class_report['Draw']['f1-score']:.3f}")

            # Store models
            self.models = models
            self.scalers['features'] = scaler

            # Save to database
            model_data = {
                'models': models,
                'scalers': self.scalers,
                'feature_columns': self.feature_columns
            }

            version = datetime.now().strftime("%Y%m%d_%H%M%S") + "_draw_opt"
            saved = self._save_model_to_db(model_name, version, model_type, model_data,
                                         len(X_train), accuracies['outcome'])

            result = {
                'success': True,
                'model_name': model_name,
                'version': version,
                'model_type': model_type,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'total_features': len(self.feature_columns),
                'accuracies': accuracies,
                'draw_performance': class_report['Draw'],
                'confusion_matrix': conf_matrix.tolist(),
                'saved_to_db': saved
            }

            logger.info("🎉 Draw-optimized model training completed successfully!")
            return result

        except Exception as e:
            logger.error(f"❌ Error in draw-optimized training: {e}")
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")
            return {'success': False, 'error': str(e)}

    def _add_draw_specific_features(self, df: pd.DataFrame, features_df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        Add features specifically designed to improve draw predictions.

        Args:
            df: Original match data
            features_df: Current features DataFrame

        Returns:
            DataFrame with draw-specific features or None if error
        """
        try:
            draw_features = pd.DataFrame(index=df.index)

            # Feature 1: Team strength similarity
            # Teams of similar strength are more likely to draw
            home_strength = features_df['home_attack_index'] + features_df['home_form_points']
            away_strength = features_df['away_attack_index'] + features_df['away_form_points']
            strength_diff = abs(home_strength - away_strength)
            draw_features['teams_strength_similarity'] = 1.0 / (1.0 + strength_diff)

            # Feature 2: Defensive strength ratio
            # Strong defenses on both sides lead to more draws
            home_defense = 1.0 / (features_df['home_form_goals_conceded'] + 1.0)
            away_defense = 1.0 / (features_df['away_form_goals_conceded'] + 1.0)
            draw_features['defensive_strength_combined'] = (home_defense + away_defense) / 2

            # Feature 3: Recent draw tendency
            # Teams that draw frequently are more likely to draw again
            draw_features['recent_draw_tendency'] = self._calculate_recent_draw_tendency(df)

            # Feature 4: Low-scoring game indicator
            # Games with low expected goals are more likely to be draws
            expected_total_goals = features_df['home_form_goals_scored'] + features_df['away_form_goals_scored']
            draw_features['low_scoring_indicator'] = 1.0 / (1.0 + expected_total_goals)

            # Feature 5: Balanced possession indicator
            # Games with similar possession are more likely to be draws
            possession_balance = 1.0 - abs(features_df['possession_diff']) / 100.0
            draw_features['possession_balance'] = possession_balance

            # Feature 6: Cards balance (disciplined games)
            # Games with few cards often end in draws
            total_cards = features_df['home_yellow_cards'] + features_df['away_yellow_cards'] + \
                         (features_df['home_red_cards'] + features_df['away_red_cards']) * 2
            draw_features['disciplined_game'] = 1.0 / (1.0 + total_cards)

            # Fill any NaN values
            draw_features = draw_features.fillna(0)

            logger.info(f"   ✅ Created {len(draw_features.columns)} draw-specific features")
            return draw_features

        except Exception as e:
            logger.error(f"Error creating draw-specific features: {e}")
            return None

    def _calculate_recent_draw_tendency(self, df: pd.DataFrame, lookback_games: int = 10) -> pd.Series:
        """
        Calculate recent draw tendency for teams.

        Args:
            df: Match data DataFrame
            lookback_games: Number of recent games to consider

        Returns:
            Series with draw tendency values
        """
        try:
            draw_tendency = pd.Series(index=df.index, dtype=float)

            # Sort by date
            df_sorted = df.sort_values('date').reset_index(drop=True)

            for idx, match in df_sorted.iterrows():
                home_team_id = match['home_team_id']
                away_team_id = match['away_team_id']
                match_date = match['date']

                # Get recent draws for both teams
                home_draws = self._get_team_recent_draws(df_sorted, home_team_id, match_date, lookback_games)
                away_draws = self._get_team_recent_draws(df_sorted, away_team_id, match_date, lookback_games)

                # Combined draw tendency
                combined_tendency = (home_draws + away_draws) / 2.0
                draw_tendency.iloc[idx] = combined_tendency

            return draw_tendency.fillna(0)

        except Exception as e:
            logger.error(f"Error calculating draw tendency: {e}")
            return pd.Series(index=df.index, dtype=float).fillna(0)

    def _get_team_recent_draws(self, df: pd.DataFrame, team_id: int, match_date, num_games: int = 10) -> float:
        """
        Get recent draw percentage for a team.

        Args:
            df: Match data DataFrame
            team_id: Team ID
            match_date: Current match date
            num_games: Number of recent games to consider

        Returns:
            Draw percentage (0.0 to 1.0)
        """
        try:
            # Get recent matches for this team
            recent_matches = df[
                ((df['home_team_id'] == team_id) | (df['away_team_id'] == team_id)) &
                (df['date'] < match_date) &
                (df['home_goals'].notna()) &
                (df['away_goals'].notna())
            ].tail(num_games)

            if len(recent_matches) == 0:
                return 0.0

            draws = 0
            for _, match in recent_matches.iterrows():
                if match['home_goals'] == match['away_goals']:
                    draws += 1

            return draws / len(recent_matches)

        except Exception as e:
            logger.error(f"Error calculating team recent draws: {e}")
            return 0.0
