"""
Football match predictor using trained models.
"""
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from database import get_db_cursor
from database.models import UpcomingGame, Prediction, PredictionOutcome
from .model_manager import ModelManager

logger = logging.getLogger(__name__)


class FootballPredictor:
    """Makes predictions for football matches using trained models."""

    def __init__(self):
        """Initialize predictor."""
        self.model_manager = ModelManager()
        self.is_loaded = False

    def load_model(self, model_name: str = "football_predictor") -> bool:
        """
        Load prediction model.

        Args:
            model_name: Name of the model to load

        Returns:
            True if loaded successfully, False otherwise
        """
        self.is_loaded = self.model_manager.load_model(model_name)
        return self.is_loaded

    def get_upcoming_games_without_predictions(self) -> List[Dict[str, Any]]:
        """
        Get upcoming games that don't have predictions yet.

        Returns:
            List of upcoming games without predictions
        """
        try:
            with get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT ug.*,
                           ht.name as home_team_name,
                           at.name as away_team_name,
                           l.name as league_name
                    FROM upcoming_games ug
                    JOIN teams ht ON ug.home_team_id = ht.id
                    JOIN teams at ON ug.away_team_id = at.id
                    JOIN leagues l ON ug.league_id = l.id AND ug.season = l.season
                    WHERE ug.status = 'NS'
                    AND ug.date > NOW()
                    AND NOT EXISTS (
                        SELECT 1 FROM predictions p
                        WHERE p.match_id = ug.id
                    )
                    ORDER BY ug.date ASC
                    LIMIT 50
                """)

                games = cursor.fetchall()
                return [dict(game) for game in games]

        except Exception as e:
            logger.error(f"Error getting upcoming games without predictions: {e}")
            return []

    def create_prediction_features(self, game: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """
        Create features for a single game prediction.

        Args:
            game: Game data dictionary

        Returns:
            DataFrame with features or None if error
        """
        try:
            # Get team statistics from recent matches
            home_stats = self._get_team_recent_stats(game['home_team_id'], game['league_id'])
            away_stats = self._get_team_recent_stats(game['away_team_id'], game['league_id'])

            # Create feature vector
            features = {
                'home_team_id': game['home_team_id'],
                'away_team_id': game['away_team_id'],
                'league_id': game['league_id'],
                'season': game['season'],
                'home_shots': home_stats.get('avg_shots', 10),
                'away_shots': away_stats.get('avg_shots', 10),
                'home_shots_on_target': home_stats.get('avg_shots_on_target', 4),
                'away_shots_on_target': away_stats.get('avg_shots_on_target', 4),
                'home_possession': home_stats.get('avg_possession', 50.0),
                'away_possession': away_stats.get('avg_possession', 50.0),
                'home_corners': home_stats.get('avg_corners', 5),
                'away_corners': away_stats.get('avg_corners', 5),
                'home_fouls': home_stats.get('avg_fouls', 12),
                'away_fouls': away_stats.get('avg_fouls', 12),
                'home_yellow_cards': home_stats.get('avg_yellow_cards', 2),
                'away_yellow_cards': away_stats.get('avg_yellow_cards', 2),
                'home_red_cards': home_stats.get('avg_red_cards', 0),
                'away_red_cards': away_stats.get('avg_red_cards', 0),
            }

            # Calculate derived features
            features['shots_ratio'] = features['home_shots'] / (features['away_shots'] + 1)
            features['shots_on_target_ratio'] = features['home_shots_on_target'] / (features['away_shots_on_target'] + 1)
            features['possession_diff'] = features['home_possession'] - features['away_possession']
            features['corners_ratio'] = features['home_corners'] / (features['away_corners'] + 1)
            features['fouls_diff'] = features['home_fouls'] - features['away_fouls']
            features['cards_diff'] = (features['home_yellow_cards'] + features['home_red_cards'] * 2) - \
                                   (features['away_yellow_cards'] + features['away_red_cards'] * 2)

            # Advanced derived features for better prediction
            features['shot_accuracy_home'] = features['home_shots_on_target'] / (features['home_shots'] + 1)
            features['shot_accuracy_away'] = features['away_shots_on_target'] / (features['away_shots'] + 1)
            features['shot_accuracy_diff'] = features['shot_accuracy_home'] - features['shot_accuracy_away']

            # Attacking vs Defensive balance
            features['home_attack_index'] = (features['home_shots'] + features['home_corners']) / 2
            features['away_attack_index'] = (features['away_shots'] + features['away_corners']) / 2
            features['attack_balance'] = features['home_attack_index'] / (features['away_attack_index'] + 1)

            # Discipline features
            features['home_discipline'] = features['home_fouls'] + features['home_yellow_cards'] * 2 + features['home_red_cards'] * 5
            features['away_discipline'] = features['away_fouls'] + features['away_yellow_cards'] * 2 + features['away_red_cards'] * 5
            features['discipline_diff'] = features['home_discipline'] - features['away_discipline']

            # Create DataFrame with correct column order
            df = pd.DataFrame([features])
            df = df[self.model_manager.feature_columns]

            return df

        except Exception as e:
            logger.error(f"Error creating prediction features: {e}")
            return None

    def _get_team_recent_stats(self, team_id: int, league_id: int, num_matches: int = 5) -> Dict[str, float]:
        """
        Get recent statistics for a team.

        Args:
            team_id: Team ID
            league_id: League ID
            num_matches: Number of recent matches to consider

        Returns:
            Dictionary with average statistics
        """
        try:
            with get_db_cursor() as cursor:
                # Get recent home matches
                cursor.execute("""
                    SELECT home_shots, home_shots_on_target, home_possession,
                           home_corners, home_fouls, home_yellow_cards, home_red_cards
                    FROM matches
                    WHERE home_team_id = %s AND league_id = %s
                    AND home_shots IS NOT NULL
                    ORDER BY date DESC
                    LIMIT %s
                """, (team_id, league_id, num_matches))

                home_matches = cursor.fetchall()

                # Get recent away matches
                cursor.execute("""
                    SELECT away_shots, away_shots_on_target, away_possession,
                           away_corners, away_fouls, away_yellow_cards, away_red_cards
                    FROM matches
                    WHERE away_team_id = %s AND league_id = %s
                    AND away_shots IS NOT NULL
                    ORDER BY date DESC
                    LIMIT %s
                """, (team_id, league_id, num_matches))

                away_matches = cursor.fetchall()

            # Calculate averages
            all_stats = []

            # Process home matches
            for match in home_matches:
                all_stats.append({
                    'shots': match['home_shots'] or 10,
                    'shots_on_target': match['home_shots_on_target'] or 4,
                    'possession': match['home_possession'] or 50.0,
                    'corners': match['home_corners'] or 5,
                    'fouls': match['home_fouls'] or 12,
                    'yellow_cards': match['home_yellow_cards'] or 2,
                    'red_cards': match['home_red_cards'] or 0,
                })

            # Process away matches
            for match in away_matches:
                all_stats.append({
                    'shots': match['away_shots'] or 10,
                    'shots_on_target': match['away_shots_on_target'] or 4,
                    'possession': match['away_possession'] or 50.0,
                    'corners': match['away_corners'] or 5,
                    'fouls': match['away_fouls'] or 12,
                    'yellow_cards': match['away_yellow_cards'] or 2,
                    'red_cards': match['away_red_cards'] or 0,
                })

            if not all_stats:
                # Return default values if no stats available
                return {
                    'avg_shots': 10,
                    'avg_shots_on_target': 4,
                    'avg_possession': 50.0,
                    'avg_corners': 5,
                    'avg_fouls': 12,
                    'avg_yellow_cards': 2,
                    'avg_red_cards': 0,
                }

            # Calculate averages
            return {
                'avg_shots': np.mean([s['shots'] for s in all_stats]),
                'avg_shots_on_target': np.mean([s['shots_on_target'] for s in all_stats]),
                'avg_possession': np.mean([s['possession'] for s in all_stats]),
                'avg_corners': np.mean([s['corners'] for s in all_stats]),
                'avg_fouls': np.mean([s['fouls'] for s in all_stats]),
                'avg_yellow_cards': np.mean([s['yellow_cards'] for s in all_stats]),
                'avg_red_cards': np.mean([s['red_cards'] for s in all_stats]),
            }

        except Exception as e:
            logger.error(f"Error getting team recent stats: {e}")
            return {
                'avg_shots': 10,
                'avg_shots_on_target': 4,
                'avg_possession': 50.0,
                'avg_corners': 5,
                'avg_fouls': 12,
                'avg_yellow_cards': 2,
                'avg_red_cards': 0,
            }

    def predict_match(self, game: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Make prediction for a single match.

        Args:
            game: Game data dictionary

        Returns:
            Dictionary with prediction results or None if error
        """
        if not self.is_loaded:
            logger.error("Model not loaded. Call load_model() first.")
            return None

        try:
            # Create features
            features_df = self.create_prediction_features(game)
            if features_df is None:
                return None

            # Scale features
            features_scaled = self.model_manager.scalers['features'].transform(features_df)

            # Make predictions
            outcome_proba = self.model_manager.models['outcome'].predict_proba(features_scaled)[0]
            home_goals_pred = self.model_manager.models['home_goals'].predict(features_scaled)[0]
            away_goals_pred = self.model_manager.models['away_goals'].predict(features_scaled)[0]

            # Ensure non-negative goals
            home_goals_pred = max(0, home_goals_pred)
            away_goals_pred = max(0, away_goals_pred)

            # Determine outcome
            predicted_outcome = np.argmax(outcome_proba)
            if predicted_outcome == 0:
                outcome_enum = PredictionOutcome.DRAW
            elif predicted_outcome == 1:
                outcome_enum = PredictionOutcome.HOME_WIN
            else:
                outcome_enum = PredictionOutcome.AWAY_WIN

            # Calculate total goals and over/under probabilities
            total_goals_pred = home_goals_pred + away_goals_pred
            over_2_5_prob = 1.0 if total_goals_pred > 2.5 else total_goals_pred / 2.5
            under_2_5_prob = 1.0 - over_2_5_prob

            prediction = {
                'match_id': game['id'],
                'predicted_outcome': outcome_enum,
                'predicted_home_goals': round(home_goals_pred, 2),
                'predicted_away_goals': round(away_goals_pred, 2),
                'predicted_total_goals': round(total_goals_pred, 2),
                'confidence_home_win': round(outcome_proba[1], 3),
                'confidence_draw': round(outcome_proba[0], 3),
                'confidence_away_win': round(outcome_proba[2], 3),
                'confidence_over_2_5': round(over_2_5_prob, 3),
                'confidence_under_2_5': round(under_2_5_prob, 3),
            }

            return prediction

        except Exception as e:
            logger.error(f"Error making prediction for match {game['id']}: {e}")
            return None

    def save_prediction(self, prediction: Dict[str, Any]) -> bool:
        """
        Save prediction to database.

        Args:
            prediction: Prediction dictionary

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            model_info = self.model_manager.get_model_info()
            model_version = f"{model_info['name']}_v{model_info['version']}" if model_info else "unknown"

            with get_db_cursor() as cursor:
                cursor.execute("""
                    INSERT INTO predictions (
                        match_id, model_version, predicted_outcome,
                        predicted_home_goals, predicted_away_goals, predicted_total_goals,
                        confidence_home_win, confidence_draw, confidence_away_win,
                        confidence_over_2_5, confidence_under_2_5
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (match_id, model_version) DO UPDATE SET
                        predicted_outcome = EXCLUDED.predicted_outcome,
                        predicted_home_goals = EXCLUDED.predicted_home_goals,
                        predicted_away_goals = EXCLUDED.predicted_away_goals,
                        predicted_total_goals = EXCLUDED.predicted_total_goals,
                        confidence_home_win = EXCLUDED.confidence_home_win,
                        confidence_draw = EXCLUDED.confidence_draw,
                        confidence_away_win = EXCLUDED.confidence_away_win,
                        confidence_over_2_5 = EXCLUDED.confidence_over_2_5,
                        confidence_under_2_5 = EXCLUDED.confidence_under_2_5,
                        updated_at = NOW()
                """, (
                    prediction['match_id'],
                    model_version,
                    prediction['predicted_outcome'].value,
                    prediction['predicted_home_goals'],
                    prediction['predicted_away_goals'],
                    prediction['predicted_total_goals'],
                    prediction['confidence_home_win'],
                    prediction['confidence_draw'],
                    prediction['confidence_away_win'],
                    prediction['confidence_over_2_5'],
                    prediction['confidence_under_2_5']
                ))

            logger.info(f"Prediction saved for match {prediction['match_id']}")
            return True

        except Exception as e:
            logger.error(f"Error saving prediction: {e}")
            return False

    def predict_upcoming_games(self) -> Dict[str, Any]:
        """
        Make predictions for all upcoming games without predictions.

        Returns:
            Dictionary with prediction results
        """
        try:
            logger.info("🔮 Starting batch prediction for upcoming games...")

            # Check if model is loaded
            if not self.is_loaded:
                logger.info("   📥 Model not loaded, attempting to load...")
                if not self.load_model():
                    logger.error("   ❌ No model available for predictions")
                    return {'success': False, 'error': 'No model available'}
                logger.info("   ✅ Model loaded successfully")

            # Get upcoming games without predictions
            logger.info("   🔍 Finding upcoming games without predictions...")
            games = self.get_upcoming_games_without_predictions()

            if not games:
                logger.info("   📋 No upcoming games need predictions")
                return {'success': True, 'predictions_made': 0, 'message': 'No upcoming games need predictions'}

            logger.info(f"   📊 Found {len(games)} games needing predictions")

            predictions_made = 0
            failed_predictions = 0

            for i, game in enumerate(games, 1):
                try:
                    logger.info(f"   🎯 Predicting game {i}/{len(games)}: {game['home_team_name']} vs {game['away_team_name']}")

                    prediction = self.predict_match(game)
                    if prediction:
                        logger.info(f"      ✅ Prediction made successfully")
                        if self.save_prediction(prediction):
                            predictions_made += 1
                            logger.info(f"      💾 Prediction saved to database")
                        else:
                            failed_predictions += 1
                            logger.warning(f"      ⚠️ Prediction made but failed to save")
                    else:
                        failed_predictions += 1
                        logger.error(f"      ❌ Failed to make prediction")

                except Exception as e:
                    logger.error(f"      ❌ Error predicting game {game['id']}: {e}")
                    failed_predictions += 1

            result = {
                'success': True,
                'predictions_made': predictions_made,
                'failed_predictions': failed_predictions,
                'total_games': len(games)
            }

            logger.info("✅ Prediction batch completed!")
            logger.info(f"   📊 Results:")
            logger.info(f"      • Total games: {len(games)}")
            logger.info(f"      • Predictions made: {predictions_made}")
            logger.info(f"      • Failed predictions: {failed_predictions}")
            logger.info(f"      • Success rate: {(predictions_made/len(games)*100):.1f}%")

            return result

        except Exception as e:
            logger.error(f"❌ Error in predict_upcoming_games: {e}")
            import traceback
            logger.error(f"📋 Full traceback: {traceback.format_exc()}")
            return {'success': False, 'error': str(e)}
