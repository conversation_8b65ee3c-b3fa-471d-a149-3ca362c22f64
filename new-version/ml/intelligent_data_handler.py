#!/usr/bin/env python3
"""
Intelligent data handler for managing missing statistics and null values in football prediction model.
"""
import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Tuple
from database.connection import get_db_cursor

logger = logging.getLogger(__name__)


class IntelligentDataHandler:
    """Handles missing data intelligently based on team/league patterns."""

    def __init__(self):
        """Initialize the intelligent data handler."""
        self.team_profiles = {}
        self.league_profiles = {}
        self.global_defaults = {}

    def analyze_data_availability(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze data availability patterns across teams and leagues.

        Args:
            df: Match data DataFrame

        Returns:
            Dictionary with data availability analysis
        """
        try:
            logger.info("🔍 Analyzing data availability patterns...")

            analysis = {
                'total_matches': len(df),
                'teams_with_stats': set(),
                'teams_without_stats': set(),
                'leagues_with_stats': set(),
                'leagues_without_stats': set(),
                'stat_columns': [],
                'missing_percentages': {}
            }

            # Define statistics columns
            stat_columns = [
                'home_shots', 'away_shots', 'home_shots_on_target', 'away_shots_on_target',
                'home_possession', 'away_possession', 'home_corners', 'away_corners',
                'home_fouls', 'away_fouls', 'home_yellow_cards', 'away_yellow_cards',
                'home_red_cards', 'away_red_cards'
            ]

            analysis['stat_columns'] = [col for col in stat_columns if col in df.columns]

            # Calculate missing percentages for each column
            for col in analysis['stat_columns']:
                missing_count = df[col].isnull().sum()
                missing_percentage = (missing_count / len(df)) * 100
                analysis['missing_percentages'][col] = missing_percentage

                logger.debug(f"   📊 {col}: {missing_percentage:.1f}% missing")

            # Analyze team-level data availability
            for _, match in df.iterrows():
                home_team_id = match['home_team_id']
                away_team_id = match['away_team_id']
                league_id = match['league_id']

                # Check if match has statistics
                has_stats = not pd.isnull(match[analysis['stat_columns']]).all()

                if has_stats:
                    analysis['teams_with_stats'].add(home_team_id)
                    analysis['teams_with_stats'].add(away_team_id)
                    analysis['leagues_with_stats'].add(league_id)
                else:
                    analysis['teams_without_stats'].add(home_team_id)
                    analysis['teams_without_stats'].add(away_team_id)
                    analysis['leagues_without_stats'].add(league_id)

            # Remove teams that appear in both sets (prioritize teams_with_stats)
            analysis['teams_without_stats'] -= analysis['teams_with_stats']
            analysis['leagues_without_stats'] -= analysis['leagues_with_stats']

            logger.info(f"📊 Data availability analysis:")
            logger.info(f"   • Teams with stats: {len(analysis['teams_with_stats'])}")
            logger.info(f"   • Teams without stats: {len(analysis['teams_without_stats'])}")
            logger.info(f"   • Leagues with stats: {len(analysis['leagues_with_stats'])}")
            logger.info(f"   • Leagues without stats: {len(analysis['leagues_without_stats'])}")

            return analysis

        except Exception as e:
            logger.error(f"Error analyzing data availability: {e}")
            return {}

    def build_team_profiles(self, df: pd.DataFrame) -> Dict[int, Dict[str, Any]]:
        """
        Build intelligent profiles for each team based on available data.

        Args:
            df: Match data DataFrame

        Returns:
            Dictionary mapping team_id to team profile
        """
        try:
            logger.info("🏗️ Building intelligent team profiles...")

            team_profiles = {}

            # Get all unique teams
            home_teams = df[['home_team_id', 'home_goals', 'away_goals']].rename(columns={
                'home_team_id': 'team_id', 'home_goals': 'goals_scored', 'away_goals': 'goals_conceded'
            })
            home_teams['is_home'] = True

            away_teams = df[['away_team_id', 'away_goals', 'home_goals']].rename(columns={
                'away_team_id': 'team_id', 'away_goals': 'goals_scored', 'home_goals': 'goals_conceded'
            })
            away_teams['is_home'] = False

            all_team_data = pd.concat([home_teams, away_teams], ignore_index=True)

            for team_id in all_team_data['team_id'].unique():
                if pd.isnull(team_id):
                    continue

                team_data = all_team_data[all_team_data['team_id'] == team_id]

                # Calculate basic statistics
                profile = {
                    'team_id': int(team_id),
                    'total_matches': len(team_data),
                    'avg_goals_scored': team_data['goals_scored'].mean() if not team_data['goals_scored'].isnull().all() else 1.0,
                    'avg_goals_conceded': team_data['goals_conceded'].mean() if not team_data['goals_conceded'].isnull().all() else 1.0,
                    'home_matches': len(team_data[team_data['is_home'] == True]),
                    'away_matches': len(team_data[team_data['is_home'] == False]),
                }

                # Calculate derived metrics
                profile['avg_goals_per_match'] = (profile['avg_goals_scored'] + profile['avg_goals_conceded']) / 2
                profile['goal_difference'] = profile['avg_goals_scored'] - profile['avg_goals_conceded']

                # Determine team strength category
                if profile['goal_difference'] > 0.5:
                    profile['strength_category'] = 'strong'
                elif profile['goal_difference'] > -0.5:
                    profile['strength_category'] = 'average'
                else:
                    profile['strength_category'] = 'weak'

                # Check data availability for this team
                team_matches = df[
                    (df['home_team_id'] == team_id) | (df['away_team_id'] == team_id)
                ]

                stat_columns = ['home_shots', 'away_shots', 'home_possession', 'away_possession']
                available_stats = []
                for col in stat_columns:
                    if col in team_matches.columns:
                        non_null_count = team_matches[col].count()
                        if non_null_count > 0:
                            available_stats.append(col)

                profile['has_detailed_stats'] = len(available_stats) > 0
                profile['available_stat_columns'] = available_stats
                profile['data_quality'] = 'high' if len(available_stats) >= 4 else 'medium' if len(available_stats) >= 2 else 'low'

                team_profiles[int(team_id)] = profile

            logger.info(f"✅ Built profiles for {len(team_profiles)} teams")

            # Log summary statistics
            high_quality = sum(1 for p in team_profiles.values() if p['data_quality'] == 'high')
            medium_quality = sum(1 for p in team_profiles.values() if p['data_quality'] == 'medium')
            low_quality = sum(1 for p in team_profiles.values() if p['data_quality'] == 'low')

            logger.info(f"📊 Team data quality distribution:")
            logger.info(f"   • High quality: {high_quality} teams")
            logger.info(f"   • Medium quality: {medium_quality} teams")
            logger.info(f"   • Low quality: {low_quality} teams")

            self.team_profiles = team_profiles
            return team_profiles

        except Exception as e:
            logger.error(f"Error building team profiles: {e}")
            return {}

    def build_league_profiles(self, df: pd.DataFrame) -> Dict[int, Dict[str, Any]]:
        """
        Build intelligent profiles for each league based on available data.

        Args:
            df: Match data DataFrame

        Returns:
            Dictionary mapping league_id to league profile
        """
        try:
            logger.info("🏆 Building intelligent league profiles...")

            league_profiles = {}

            for league_id in df['league_id'].unique():
                if pd.isnull(league_id):
                    continue

                league_data = df[df['league_id'] == league_id]

                # Calculate basic league statistics
                profile = {
                    'league_id': int(league_id),
                    'total_matches': len(league_data),
                    'avg_home_goals': league_data['home_goals'].mean() if not league_data['home_goals'].isnull().all() else 1.5,
                    'avg_away_goals': league_data['away_goals'].mean() if not league_data['away_goals'].isnull().all() else 1.0,
                    'avg_total_goals': 0,
                    'home_win_percentage': 0,
                    'draw_percentage': 0,
                    'away_win_percentage': 0
                }

                profile['avg_total_goals'] = profile['avg_home_goals'] + profile['avg_away_goals']

                # Calculate outcome percentages
                completed_matches = league_data[
                    league_data['home_goals'].notna() & league_data['away_goals'].notna()
                ]

                if len(completed_matches) > 0:
                    home_wins = len(completed_matches[completed_matches['home_goals'] > completed_matches['away_goals']])
                    draws = len(completed_matches[completed_matches['home_goals'] == completed_matches['away_goals']])
                    away_wins = len(completed_matches[completed_matches['home_goals'] < completed_matches['away_goals']])

                    total_completed = len(completed_matches)
                    profile['home_win_percentage'] = home_wins / total_completed
                    profile['draw_percentage'] = draws / total_completed
                    profile['away_win_percentage'] = away_wins / total_completed

                # Check data availability for this league
                stat_columns = ['home_shots', 'away_shots', 'home_possession', 'away_possession']
                available_stats = []
                for col in stat_columns:
                    if col in league_data.columns:
                        non_null_count = league_data[col].count()
                        if non_null_count > 0:
                            available_stats.append(col)

                profile['has_detailed_stats'] = len(available_stats) > 0
                profile['available_stat_columns'] = available_stats
                profile['data_quality'] = 'high' if len(available_stats) >= 4 else 'medium' if len(available_stats) >= 2 else 'low'

                # Determine league characteristics
                if profile['avg_total_goals'] > 3.0:
                    profile['scoring_style'] = 'high_scoring'
                elif profile['avg_total_goals'] > 2.2:
                    profile['scoring_style'] = 'average_scoring'
                else:
                    profile['scoring_style'] = 'low_scoring'

                league_profiles[int(league_id)] = profile

            logger.info(f"✅ Built profiles for {len(league_profiles)} leagues")

            self.league_profiles = league_profiles
            return league_profiles

        except Exception as e:
            logger.error(f"Error building league profiles: {e}")
            return {}

    def get_intelligent_defaults(self, team_id: int, league_id: int, is_home: bool = True) -> Dict[str, float]:
        """
        Get intelligent default values for a team based on profiles.

        Args:
            team_id: Team ID
            league_id: League ID
            is_home: Whether team is playing at home

        Returns:
            Dictionary with intelligent default values
        """
        try:
            defaults = {}

            # Get team profile
            team_profile = self.team_profiles.get(team_id, {})
            league_profile = self.league_profiles.get(league_id, {})

            # Base defaults
            if team_profile:
                # Use team-specific data
                avg_goals = team_profile.get('avg_goals_scored', 1.0)
                strength = team_profile.get('strength_category', 'average')

                # Adjust for home advantage
                if is_home:
                    avg_goals *= 1.2  # Home advantage boost
                else:
                    avg_goals *= 0.9  # Away disadvantage

            elif league_profile:
                # Use league-specific data
                if is_home:
                    avg_goals = league_profile.get('avg_home_goals', 1.5)
                else:
                    avg_goals = league_profile.get('avg_away_goals', 1.0)
                strength = 'average'
            else:
                # Global defaults
                avg_goals = 1.5 if is_home else 1.0
                strength = 'average'

            # Calculate intelligent defaults based on goals
            defaults['shots'] = max(8, avg_goals * 6)  # More goals = more shots
            defaults['shots_on_target'] = max(3, avg_goals * 2.5)
            defaults['possession'] = 50.0  # Neutral default
            defaults['corners'] = max(3, avg_goals * 3)
            defaults['fouls'] = 12  # Standard default
            defaults['yellow_cards'] = 2  # Standard default
            defaults['red_cards'] = 0  # Conservative default

            # Adjust based on team strength
            if strength == 'strong':
                defaults['shots'] *= 1.3
                defaults['shots_on_target'] *= 1.4
                defaults['possession'] = 55.0
                defaults['corners'] *= 1.2
            elif strength == 'weak':
                defaults['shots'] *= 0.7
                defaults['shots_on_target'] *= 0.6
                defaults['possession'] = 45.0
                defaults['corners'] *= 0.8
                defaults['fouls'] *= 1.1  # Weaker teams tend to foul more

            return defaults

        except Exception as e:
            logger.error(f"Error getting intelligent defaults for team {team_id}: {e}")
            return {
                'shots': 10, 'shots_on_target': 4, 'possession': 50.0,
                'corners': 5, 'fouls': 12, 'yellow_cards': 2, 'red_cards': 0
            }

    def fill_missing_data_intelligently(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Fill missing data using intelligent defaults based on team/league profiles.

        Args:
            df: DataFrame with missing data

        Returns:
            DataFrame with intelligently filled missing data
        """
        try:
            logger.info("🧠 Filling missing data intelligently...")

            # Build profiles if not already built
            if not self.team_profiles:
                self.build_team_profiles(df)
            if not self.league_profiles:
                self.build_league_profiles(df)

            df_filled = df.copy()
            filled_count = 0
            total_missing = 0

            # Define columns to fill intelligently
            stat_columns = {
                'home_shots': 'shots',
                'away_shots': 'shots',
                'home_shots_on_target': 'shots_on_target',
                'away_shots_on_target': 'shots_on_target',
                'home_possession': 'possession',
                'away_possession': 'possession',
                'home_corners': 'corners',
                'away_corners': 'corners',
                'home_fouls': 'fouls',
                'away_fouls': 'fouls',
                'home_yellow_cards': 'yellow_cards',
                'away_yellow_cards': 'yellow_cards',
                'home_red_cards': 'red_cards',
                'away_red_cards': 'red_cards'
            }

            for idx, row in df_filled.iterrows():
                home_team_id = row['home_team_id']
                away_team_id = row['away_team_id']
                league_id = row['league_id']

                # Get intelligent defaults for both teams
                home_defaults = self.get_intelligent_defaults(home_team_id, league_id, is_home=True)
                away_defaults = self.get_intelligent_defaults(away_team_id, league_id, is_home=False)

                # Fill missing values
                for col, default_key in stat_columns.items():
                    if col in df_filled.columns and pd.isnull(row[col]):
                        total_missing += 1

                        if col.startswith('home_'):
                            default_value = home_defaults.get(default_key, 0)
                        else:
                            default_value = away_defaults.get(default_key, 0)

                        # Special handling for possession (must sum to 100)
                        if 'possession' in col:
                            if pd.isnull(row['home_possession']) and pd.isnull(row['away_possession']):
                                # Both missing - use defaults
                                df_filled.at[idx, 'home_possession'] = home_defaults['possession']
                                df_filled.at[idx, 'away_possession'] = 100 - home_defaults['possession']
                                filled_count += 2
                                total_missing += 1  # Count as one logical unit
                            elif pd.isnull(row['home_possession']):
                                # Only home missing
                                df_filled.at[idx, 'home_possession'] = 100 - row['away_possession']
                                filled_count += 1
                            elif pd.isnull(row['away_possession']):
                                # Only away missing
                                df_filled.at[idx, 'away_possession'] = 100 - row['home_possession']
                                filled_count += 1
                        else:
                            df_filled.at[idx, col] = default_value
                            filled_count += 1

            logger.info(f"✅ Intelligently filled {filled_count}/{total_missing} missing values")

            # Log summary of data quality improvement
            self._log_data_quality_improvement(df, df_filled)

            return df_filled

        except Exception as e:
            logger.error(f"Error filling missing data intelligently: {e}")
            return df

    def _log_data_quality_improvement(self, df_original: pd.DataFrame, df_filled: pd.DataFrame):
        """Log the improvement in data quality after intelligent filling."""
        try:
            stat_columns = [
                'home_shots', 'away_shots', 'home_shots_on_target', 'away_shots_on_target',
                'home_possession', 'away_possession', 'home_corners', 'away_corners',
                'home_fouls', 'away_fouls', 'home_yellow_cards', 'away_yellow_cards',
                'home_red_cards', 'away_red_cards'
            ]

            logger.info("📊 Data quality improvement summary:")

            for col in stat_columns:
                if col in df_original.columns:
                    original_missing = df_original[col].isnull().sum()
                    filled_missing = df_filled[col].isnull().sum()
                    improvement = original_missing - filled_missing

                    if improvement > 0:
                        original_pct = (original_missing / len(df_original)) * 100
                        filled_pct = (filled_missing / len(df_filled)) * 100
                        logger.info(f"   • {col}: {original_pct:.1f}% → {filled_pct:.1f}% missing ({improvement} values filled)")

        except Exception as e:
            logger.debug(f"Error logging data quality improvement: {e}")

    def create_alternative_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create alternative features when detailed statistics are not available.

        Args:
            df: DataFrame with basic match data

        Returns:
            DataFrame with alternative features added
        """
        try:
            logger.info("🔧 Creating alternative features for missing statistics...")

            df_enhanced = df.copy()

            # Build profiles if not already built
            if not self.team_profiles:
                self.build_team_profiles(df)
            if not self.league_profiles:
                self.build_league_profiles(df)

            # Create team strength indicators
            df_enhanced['home_team_strength'] = df_enhanced['home_team_id'].apply(
                lambda x: self._get_team_strength_score(x)
            )
            df_enhanced['away_team_strength'] = df_enhanced['away_team_id'].apply(
                lambda x: self._get_team_strength_score(x)
            )
            df_enhanced['strength_difference'] = df_enhanced['home_team_strength'] - df_enhanced['away_team_strength']

            # Create league-based features
            df_enhanced['league_avg_goals'] = df_enhanced['league_id'].apply(
                lambda x: self._get_league_avg_goals(x)
            )
            df_enhanced['league_home_advantage'] = df_enhanced['league_id'].apply(
                lambda x: self._get_league_home_advantage(x)
            )

            # Create goal-based features (when available)
            if 'home_goals' in df_enhanced.columns and 'away_goals' in df_enhanced.columns:
                df_enhanced['total_goals'] = df_enhanced['home_goals'] + df_enhanced['away_goals']
                df_enhanced['goal_difference'] = df_enhanced['home_goals'] - df_enhanced['away_goals']
                df_enhanced['high_scoring'] = (df_enhanced['total_goals'] > 2.5).astype(int)
                df_enhanced['home_win'] = (df_enhanced['home_goals'] > df_enhanced['away_goals']).astype(int)
                df_enhanced['draw'] = (df_enhanced['home_goals'] == df_enhanced['away_goals']).astype(int)
                df_enhanced['away_win'] = (df_enhanced['home_goals'] < df_enhanced['away_goals']).astype(int)

            logger.info(f"✅ Created {len(df_enhanced.columns) - len(df.columns)} alternative features")

            return df_enhanced

        except Exception as e:
            logger.error(f"Error creating alternative features: {e}")
            return df

    def _get_team_strength_score(self, team_id: int) -> float:
        """Get a strength score for a team based on its profile."""
        try:
            profile = self.team_profiles.get(team_id, {})
            if not profile:
                return 0.5  # Neutral strength

            goal_diff = profile.get('goal_difference', 0)
            # Normalize goal difference to 0-1 scale
            strength = 0.5 + (goal_diff / 4.0)  # Assuming max realistic goal diff is ±2
            return max(0.0, min(1.0, strength))  # Clamp to 0-1 range

        except Exception:
            return 0.5

    def _get_league_avg_goals(self, league_id: int) -> float:
        """Get average goals per match for a league."""
        try:
            profile = self.league_profiles.get(league_id, {})
            return profile.get('avg_total_goals', 2.5)
        except Exception:
            return 2.5

    def _get_league_home_advantage(self, league_id: int) -> float:
        """Get home advantage factor for a league."""
        try:
            profile = self.league_profiles.get(league_id, {})
            home_win_pct = profile.get('home_win_percentage', 0.45)
            # Convert to advantage factor (0.5 = no advantage, >0.5 = home advantage)
            return home_win_pct
        except Exception:
            return 0.45
