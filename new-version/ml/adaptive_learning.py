"""
Adaptive Learning System for football prediction models.
Based on the ADAPTIVE_LEARNING.md specifications.
"""
import logging
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd

from database import get_db_cursor
from database.models import PredictionOutcome
from .model_manager import ModelManager

logger = logging.getLogger(__name__)


class AdaptiveLearningSystem:
    """
    Adaptive Learning System that automatically improves the model by learning from predictions.
    """

    def __init__(self, learning_rate: float = 0.1, confidence_threshold: float = 0.8,
                 min_accuracy_threshold: float = 0.6, high_confidence_threshold: float = 0.75):
        """
        Initialize adaptive learning system.

        Args:
            learning_rate: How aggressively to learn (0.0-1.0)
            confidence_threshold: Min confidence for high quality samples
            min_accuracy_threshold: Min accuracy before retraining trigger
            high_confidence_threshold: Min high-confidence accuracy
        """
        self.learning_rate = learning_rate
        self.confidence_threshold = confidence_threshold
        self.min_accuracy_threshold = min_accuracy_threshold
        self.high_confidence_threshold = high_confidence_threshold

        self.model_manager = ModelManager()
        self.is_loaded = False

    def load_model(self, model_name: str = "football_predictor") -> bool:
        """
        Load model for adaptive learning.

        Args:
            model_name: Name of the model to load

        Returns:
            True if loaded successfully, False otherwise
        """
        self.is_loaded = self.model_manager.load_model(model_name)
        return self.is_loaded

    def analyze_prediction_performance(self, days_back: int = 30) -> Optional[Dict[str, Any]]:
        """
        Analyze prediction performance over a specified period.

        Args:
            days_back: Number of days to look back

        Returns:
            Dictionary with performance analysis or None if insufficient data
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_back)

            with get_db_cursor() as cursor:
                # Get predictions with actual results
                cursor.execute("""
                    SELECT
                        predicted_outcome,
                        actual_outcome,
                        predicted_home_goals,
                        predicted_away_goals,
                        actual_home_goals,
                        actual_away_goals,
                        confidence_home_win,
                        confidence_draw,
                        confidence_away_win,
                        outcome_accuracy,
                        goals_accuracy,
                        total_accuracy
                    FROM predictions
                    WHERE prediction_date >= %s
                    AND actual_outcome IS NOT NULL
                    AND actual_home_goals IS NOT NULL
                    AND actual_away_goals IS NOT NULL
                """, (cutoff_date,))

                predictions = cursor.fetchall()

            if len(predictions) < 10:
                logger.warning(f"Insufficient prediction data for analysis: {len(predictions)} predictions")
                return None

            # Calculate performance metrics
            total_predictions = len(predictions)
            correct_outcomes = sum(1 for p in predictions if p['predicted_outcome'] == p['actual_outcome'])
            outcome_accuracy = correct_outcomes / total_predictions

            # Goals accuracy (within 1 goal tolerance)
            goals_accurate = 0
            for p in predictions:
                home_diff = abs(p['predicted_home_goals'] - p['actual_home_goals'])
                away_diff = abs(p['predicted_away_goals'] - p['actual_away_goals'])
                if home_diff <= 1.0 and away_diff <= 1.0:
                    goals_accurate += 1

            goals_accuracy = goals_accurate / total_predictions

            # High confidence predictions accuracy
            high_confidence_predictions = []
            for p in predictions:
                max_confidence = max(p['confidence_home_win'], p['confidence_draw'], p['confidence_away_win'])
                if max_confidence >= self.confidence_threshold:
                    high_confidence_predictions.append(p)

            if high_confidence_predictions:
                high_conf_correct = sum(1 for p in high_confidence_predictions
                                      if p['predicted_outcome'] == p['actual_outcome'])
                high_confidence_accuracy = high_conf_correct / len(high_confidence_predictions)
            else:
                high_confidence_accuracy = 0.0

            # Confidence calibration
            confidence_calibration = self._calculate_confidence_calibration(predictions)

            analysis = {
                'total_predictions': total_predictions,
                'outcome_accuracy': outcome_accuracy,
                'goals_accuracy': goals_accuracy,
                'high_confidence_accuracy': high_confidence_accuracy,
                'high_confidence_count': len(high_confidence_predictions),
                'confidence_calibration': confidence_calibration,
                'analysis_period_days': days_back
            }

            logger.info(f"Performance analysis completed: {analysis}")
            return analysis

        except Exception as e:
            logger.error(f"Error analyzing prediction performance: {e}")
            return None

    def _calculate_confidence_calibration(self, predictions: List[Dict[str, Any]]) -> float:
        """
        Calculate confidence calibration score.

        Args:
            predictions: List of prediction dictionaries

        Returns:
            Confidence calibration score (0.0-1.0, higher is better)
        """
        try:
            if not predictions:
                return 0.0

            # Group predictions by confidence bins
            bins = np.linspace(0, 1, 11)  # 10 bins
            bin_accuracies = []
            bin_confidences = []

            for i in range(len(bins) - 1):
                bin_min, bin_max = bins[i], bins[i + 1]
                bin_predictions = []

                for p in predictions:
                    max_confidence = max(p['confidence_home_win'], p['confidence_draw'], p['confidence_away_win'])
                    if bin_min <= max_confidence < bin_max:
                        bin_predictions.append(p)

                if bin_predictions:
                    bin_accuracy = sum(1 for p in bin_predictions
                                     if p['predicted_outcome'] == p['actual_outcome']) / len(bin_predictions)
                    bin_confidence = (bin_min + bin_max) / 2

                    bin_accuracies.append(bin_accuracy)
                    bin_confidences.append(bin_confidence)

            if not bin_accuracies:
                return 0.0

            # Calculate calibration error (lower is better)
            calibration_error = np.mean([abs(acc - conf) for acc, conf in zip(bin_accuracies, bin_confidences)])

            # Convert to calibration score (higher is better)
            calibration_score = max(0.0, 1.0 - calibration_error)

            return calibration_score

        except Exception as e:
            logger.error(f"Error calculating confidence calibration: {e}")
            return 0.0

    def intelligent_sample_selection(self, max_samples: int = 50) -> List[Dict[str, Any]]:
        """
        Intelligently select the most valuable samples for training.

        Args:
            max_samples: Maximum number of samples to select

        Returns:
            List of selected training samples
        """
        try:
            with get_db_cursor() as cursor:
                # Get predictions with actual results
                cursor.execute("""
                    SELECT
                        p.*,
                        m.home_team_id,
                        m.away_team_id,
                        m.league_id,
                        m.season,
                        m.date as match_date,
                        m.home_shots,
                        m.away_shots,
                        m.home_shots_on_target,
                        m.away_shots_on_target,
                        m.home_possession,
                        m.away_possession,
                        m.home_corners,
                        m.away_corners,
                        m.home_fouls,
                        m.away_fouls,
                        m.home_yellow_cards,
                        m.away_yellow_cards,
                        m.home_red_cards,
                        m.away_red_cards
                    FROM predictions p
                    JOIN matches m ON p.match_id = m.id
                    WHERE p.actual_outcome IS NOT NULL
                    AND p.actual_home_goals IS NOT NULL
                    AND p.actual_away_goals IS NOT NULL
                    AND m.home_shots IS NOT NULL
                    ORDER BY p.prediction_date DESC
                    LIMIT 200
                """)

                samples = cursor.fetchall()

            if not samples:
                logger.warning("No samples available for selection")
                return []

            # Score each sample for learning value
            scored_samples = []

            for sample in samples:
                score = self._calculate_sample_score(sample)
                scored_samples.append((score, dict(sample)))

            # Sort by score (highest first) and select top samples
            scored_samples.sort(key=lambda x: x[0], reverse=True)
            selected_samples = [sample for _, sample in scored_samples[:max_samples]]

            logger.info(f"Selected {len(selected_samples)} high-value samples for training")
            return selected_samples

        except Exception as e:
            logger.error(f"Error in intelligent sample selection: {e}")
            return []

    def _calculate_sample_score(self, sample: Dict[str, Any]) -> float:
        """
        Calculate learning value score for a sample.

        Args:
            sample: Sample data dictionary

        Returns:
            Learning value score (higher is more valuable)
        """
        try:
            score = 0.0

            # Recency score (more recent = higher score)
            days_ago = (datetime.now() - sample['prediction_date']).days
            recency_score = max(0.0, 1.0 - (days_ago / 90.0))  # Decay over 90 days
            score += recency_score * 0.3

            # Mistake score (incorrect predictions are more valuable)
            is_outcome_wrong = sample['predicted_outcome'] != sample['actual_outcome']
            if is_outcome_wrong:
                score += 0.4

            # Overconfidence penalty (high confidence mistakes are very valuable)
            max_confidence = max(sample['confidence_home_win'], sample['confidence_draw'], sample['confidence_away_win'])
            if is_outcome_wrong and max_confidence > self.confidence_threshold:
                score += 0.3

            # Edge case bonus (draws and high-scoring games are rare and valuable)
            if sample['actual_outcome'] == PredictionOutcome.DRAW.value:
                score += 0.2

            total_goals = sample['actual_home_goals'] + sample['actual_away_goals']
            if total_goals >= 5:  # High-scoring games
                score += 0.2
            elif total_goals == 0:  # Goalless draws
                score += 0.3

            # Quality bonus (samples with complete statistics)
            if (sample['home_shots'] is not None and
                sample['home_possession'] is not None and
                sample['home_corners'] is not None):
                score += 0.1

            return min(1.0, score)  # Cap at 1.0

        except Exception as e:
            logger.error(f"Error calculating sample score: {e}")
            return 0.0

    def adaptive_retraining(self) -> Dict[str, Any]:
        """
        Perform adaptive retraining based on performance analysis.

        Returns:
            Dictionary with retraining results
        """
        try:
            if not self.is_loaded:
                return {'success': False, 'error': 'Model not loaded'}

            # Analyze current performance
            analysis = self.analyze_prediction_performance(days_back=30)
            if not analysis:
                return {'success': False, 'error': 'Insufficient data for analysis'}

            # Determine if retraining is needed
            retrain_reasons = []

            if analysis['outcome_accuracy'] < self.min_accuracy_threshold:
                retrain_reasons.append(f"Low outcome accuracy: {analysis['outcome_accuracy']:.3f}")

            if (analysis['high_confidence_count'] > 5 and
                analysis['high_confidence_accuracy'] < self.high_confidence_threshold):
                retrain_reasons.append(f"Low high-confidence accuracy: {analysis['high_confidence_accuracy']:.3f}")

            if analysis['confidence_calibration'] < 0.7:
                retrain_reasons.append(f"Poor confidence calibration: {analysis['confidence_calibration']:.3f}")

            if not retrain_reasons:
                return {
                    'success': True,
                    'retrained': False,
                    'message': 'Model performance is satisfactory',
                    'analysis': analysis
                }

            logger.info(f"Retraining triggered. Reasons: {retrain_reasons}")

            # Select high-value samples for retraining
            samples = self.intelligent_sample_selection(max_samples=100)
            if len(samples) < 20:
                return {'success': False, 'error': 'Insufficient samples for retraining'}

            # Perform retraining with adaptive learning rate
            adaptive_lr = min(0.3, self.learning_rate * (2.0 - analysis['outcome_accuracy']))

            retrain_result = self._retrain_with_samples(samples, adaptive_lr)

            # Log learning history
            self._log_learning_history(analysis, retrain_result, retrain_reasons)

            return {
                'success': True,
                'retrained': True,
                'retrain_reasons': retrain_reasons,
                'retrain_results': retrain_result,
                'analysis': analysis
            }

        except Exception as e:
            logger.error(f"Error in adaptive retraining: {e}")
            return {'success': False, 'error': str(e)}

    def _retrain_with_samples(self, samples: List[Dict[str, Any]], learning_rate: float) -> Dict[str, Any]:
        """
        Retrain model with selected samples.

        Args:
            samples: Training samples
            learning_rate: Adaptive learning rate

        Returns:
            Dictionary with retraining results
        """
        try:
            # This is a simplified retraining approach
            # In a full implementation, you would:
            # 1. Create features from samples
            # 2. Combine with existing training data
            # 3. Retrain the model with adjusted parameters
            # 4. Validate improvements

            logger.info(f"Retraining with {len(samples)} samples and learning rate {learning_rate}")

            # For now, we'll simulate retraining by triggering a new model training
            # with the current data plus emphasis on the selected samples
            model_info = self.model_manager.get_model_info()
            model_type = model_info['model_type'] if model_info else 'xgboost'

            # Train new model
            result = self.model_manager.train_model(
                model_name="football_predictor",
                model_type=model_type
            )

            if result['success']:
                # Reload the new model
                self.load_model()

                return {
                    'success': True,
                    'samples_used': len(samples),
                    'learning_rate': learning_rate,
                    'new_accuracy': result.get('accuracies', {}).get('outcome', 0.0)
                }
            else:
                return {'success': False, 'error': result.get('error', 'Unknown error')}

        except Exception as e:
            logger.error(f"Error in retraining with samples: {e}")
            return {'success': False, 'error': str(e)}

    def _log_learning_history(self, analysis: Dict[str, Any], retrain_result: Dict[str, Any],
                             reasons: List[str]) -> None:
        """
        Log learning history to database.

        Args:
            analysis: Performance analysis
            retrain_result: Retraining results
            reasons: Reasons for retraining
        """
        try:
            model_info = self.model_manager.get_model_info()
            model_version = f"{model_info['name']}_v{model_info['version']}" if model_info else "unknown"

            accuracy_before = analysis['outcome_accuracy']
            accuracy_after = retrain_result.get('new_accuracy', accuracy_before)
            improvement = accuracy_after - accuracy_before

            learning_params = {
                'learning_rate': self.learning_rate,
                'confidence_threshold': self.confidence_threshold,
                'min_accuracy_threshold': self.min_accuracy_threshold,
                'high_confidence_threshold': self.high_confidence_threshold
            }

            with get_db_cursor() as cursor:
                cursor.execute("""
                    INSERT INTO learning_history (
                        model_version, samples_used, accuracy_before, accuracy_after,
                        improvement, learning_reason, learning_parameters
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (
                    model_version,
                    retrain_result.get('samples_used', 0),
                    accuracy_before,
                    accuracy_after,
                    improvement,
                    '; '.join(reasons),
                    json.dumps(learning_params)
                ))

            logger.info(f"Learning history logged: improvement {improvement:.3f}")

        except Exception as e:
            logger.error(f"Error logging learning history: {e}")


def run_adaptive_learning() -> Dict[str, Any]:
    """
    Run the adaptive learning cycle.

    Returns:
        Dictionary with learning results
    """
    try:
        learning_system = AdaptiveLearningSystem()

        # Load model
        if not learning_system.load_model():
            return {'success': False, 'error': 'No model available for adaptive learning'}

        # Run adaptive retraining
        result = learning_system.adaptive_retraining()

        if result['success']:
            if result.get('retrained', False):
                logger.info("Adaptive learning completed: Model improved")
                return {
                    'success': True,
                    'learned': True,
                    'retrain_results': result.get('retrain_results', {}),
                    'reasons': result.get('retrain_reasons', [])
                }
            else:
                logger.info("Adaptive learning completed: No improvement needed")
                return {
                    'success': True,
                    'learned': False,
                    'message': result.get('message', 'Model performance is satisfactory')
                }
        else:
            return result

    except Exception as e:
        logger.error(f"Error in adaptive learning: {e}")
        return {'success': False, 'error': str(e)}
