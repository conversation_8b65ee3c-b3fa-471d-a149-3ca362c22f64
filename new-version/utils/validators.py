"""
Data validation utilities.
"""
import re
from datetime import datetime, date
from typing import Optional, Union


def validate_email(email: str) -> bool:
    """
    Validate email address format.
    
    Args:
        email: Email address to validate
        
    Returns:
        True if valid, False otherwise
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_date(date_str: str, format_str: str = '%Y-%m-%d') -> Optional[date]:
    """
    Validate and parse date string.
    
    Args:
        date_str: Date string to validate
        format_str: Expected date format
        
    Returns:
        Parsed date object or None if invalid
    """
    try:
        return datetime.strptime(date_str, format_str).date()
    except ValueError:
        return None


def validate_datetime(datetime_str: str, format_str: str = '%Y-%m-%d %H:%M:%S') -> Optional[datetime]:
    """
    Validate and parse datetime string.
    
    Args:
        datetime_str: Datetime string to validate
        format_str: Expected datetime format
        
    Returns:
        Parsed datetime object or None if invalid
    """
    try:
        return datetime.strptime(datetime_str, format_str)
    except ValueError:
        return None


def validate_positive_int(value: Union[str, int]) -> Optional[int]:
    """
    Validate positive integer.
    
    Args:
        value: Value to validate
        
    Returns:
        Validated integer or None if invalid
    """
    try:
        int_value = int(value)
        return int_value if int_value > 0 else None
    except (ValueError, TypeError):
        return None


def validate_positive_float(value: Union[str, float]) -> Optional[float]:
    """
    Validate positive float.
    
    Args:
        value: Value to validate
        
    Returns:
        Validated float or None if invalid
    """
    try:
        float_value = float(value)
        return float_value if float_value > 0 else None
    except (ValueError, TypeError):
        return None


def validate_percentage(value: Union[str, float]) -> Optional[float]:
    """
    Validate percentage value (0-100).
    
    Args:
        value: Value to validate
        
    Returns:
        Validated percentage or None if invalid
    """
    try:
        float_value = float(value)
        return float_value if 0 <= float_value <= 100 else None
    except (ValueError, TypeError):
        return None


def validate_probability(value: Union[str, float]) -> Optional[float]:
    """
    Validate probability value (0.0-1.0).
    
    Args:
        value: Value to validate
        
    Returns:
        Validated probability or None if invalid
    """
    try:
        float_value = float(value)
        return float_value if 0.0 <= float_value <= 1.0 else None
    except (ValueError, TypeError):
        return None


def validate_team_id(team_id: Union[str, int]) -> Optional[int]:
    """
    Validate team ID.
    
    Args:
        team_id: Team ID to validate
        
    Returns:
        Validated team ID or None if invalid
    """
    return validate_positive_int(team_id)


def validate_league_id(league_id: Union[str, int]) -> Optional[int]:
    """
    Validate league ID.
    
    Args:
        league_id: League ID to validate
        
    Returns:
        Validated league ID or None if invalid
    """
    return validate_positive_int(league_id)


def validate_season(season: Union[str, int]) -> Optional[int]:
    """
    Validate season year.
    
    Args:
        season: Season year to validate
        
    Returns:
        Validated season or None if invalid
    """
    try:
        season_int = int(season)
        current_year = datetime.now().year
        # Allow seasons from 2000 to 5 years in the future
        return season_int if 2000 <= season_int <= current_year + 5 else None
    except (ValueError, TypeError):
        return None
