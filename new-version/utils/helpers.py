"""
Helper utility functions.
"""
from datetime import datetime, date
from typing import Optional, Union, Any
import json


def format_datetime(dt: Optional[datetime], format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
    """
    Format datetime object to string.
    
    Args:
        dt: Datetime object to format
        format_str: Format string
        
    Returns:
        Formatted datetime string or 'N/A' if None
    """
    if dt is None:
        return 'N/A'
    return dt.strftime(format_str)


def format_date(d: Optional[date], format_str: str = '%Y-%m-%d') -> str:
    """
    Format date object to string.
    
    Args:
        d: Date object to format
        format_str: Format string
        
    Returns:
        Formatted date string or 'N/A' if None
    """
    if d is None:
        return 'N/A'
    return d.strftime(format_str)


def format_percentage(value: Optional[float], decimal_places: int = 1) -> str:
    """
    Format float as percentage.
    
    Args:
        value: Value to format (0.0-1.0)
        decimal_places: Number of decimal places
        
    Returns:
        Formatted percentage string or 'N/A' if None
    """
    if value is None:
        return 'N/A'
    return f"{value * 100:.{decimal_places}f}%"


def format_currency(value: Optional[float], currency: str = '$', decimal_places: int = 2) -> str:
    """
    Format float as currency.
    
    Args:
        value: Value to format
        currency: Currency symbol
        decimal_places: Number of decimal places
        
    Returns:
        Formatted currency string or 'N/A' if None
    """
    if value is None:
        return 'N/A'
    return f"{currency}{value:,.{decimal_places}f}"


def safe_divide(numerator: Optional[float], denominator: Optional[float], default: float = 0.0) -> float:
    """
    Safely divide two numbers, handling None and zero division.
    
    Args:
        numerator: Numerator value
        denominator: Denominator value
        default: Default value if division is not possible
        
    Returns:
        Division result or default value
    """
    if numerator is None or denominator is None or denominator == 0:
        return default
    return numerator / denominator


def safe_percentage(part: Optional[float], total: Optional[float], default: float = 0.0) -> float:
    """
    Safely calculate percentage, handling None and zero division.
    
    Args:
        part: Part value
        total: Total value
        default: Default value if calculation is not possible
        
    Returns:
        Percentage (0.0-1.0) or default value
    """
    if part is None or total is None or total == 0:
        return default
    return min(1.0, max(0.0, part / total))


def truncate_string(text: Optional[str], max_length: int, suffix: str = '...') -> str:
    """
    Truncate string to maximum length.
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        suffix: Suffix to add if truncated
        
    Returns:
        Truncated string or 'N/A' if None
    """
    if text is None:
        return 'N/A'
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def format_number(value: Optional[Union[int, float]], decimal_places: Optional[int] = None) -> str:
    """
    Format number with thousands separators.
    
    Args:
        value: Number to format
        decimal_places: Number of decimal places (None for auto)
        
    Returns:
        Formatted number string or 'N/A' if None
    """
    if value is None:
        return 'N/A'
    
    if decimal_places is not None:
        return f"{value:,.{decimal_places}f}"
    elif isinstance(value, int):
        return f"{value:,}"
    else:
        return f"{value:,.2f}"


def parse_json_safe(json_str: Optional[str], default: Any = None) -> Any:
    """
    Safely parse JSON string.
    
    Args:
        json_str: JSON string to parse
        default: Default value if parsing fails
        
    Returns:
        Parsed JSON object or default value
    """
    if not json_str:
        return default
    
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default


def to_json_safe(obj: Any, default: str = '{}') -> str:
    """
    Safely convert object to JSON string.
    
    Args:
        obj: Object to convert
        default: Default value if conversion fails
        
    Returns:
        JSON string or default value
    """
    try:
        return json.dumps(obj, default=str)
    except (TypeError, ValueError):
        return default


def clamp(value: float, min_value: float, max_value: float) -> float:
    """
    Clamp value between min and max.
    
    Args:
        value: Value to clamp
        min_value: Minimum value
        max_value: Maximum value
        
    Returns:
        Clamped value
    """
    return max(min_value, min(max_value, value))


def get_nested_value(data: dict, keys: str, default: Any = None) -> Any:
    """
    Get nested value from dictionary using dot notation.
    
    Args:
        data: Dictionary to search
        keys: Dot-separated keys (e.g., 'user.profile.name')
        default: Default value if key not found
        
    Returns:
        Found value or default
    """
    try:
        for key in keys.split('.'):
            data = data[key]
        return data
    except (KeyError, TypeError):
        return default
