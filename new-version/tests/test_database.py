"""
Tests for database functionality.
"""
import pytest
from unittest.mock import patch, MagicMock

from database.connection import get_connection, test_connection
from database.migrations import setup_database, drop_all_tables
from database.models import Team, League, Match, MatchStatus, PredictionOutcome


class TestDatabaseConnection:
    """Test database connection functionality."""
    
    @patch('database.connection.psycopg2.pool.SimpleConnectionPool')
    def test_initialize_connection_pool(self, mock_pool):
        """Test connection pool initialization."""
        from database.connection import initialize_connection_pool
        
        mock_pool.return_value = MagicMock()
        initialize_connection_pool()
        
        mock_pool.assert_called_once()
    
    @patch('database.connection.get_connection')
    def test_connection_success(self, mock_get_connection):
        """Test successful database connection."""
        mock_connection = MagicMock()
        mock_cursor = MagicMock()
        mock_cursor.fetchone.return_value = [1]
        mock_connection.cursor.return_value = mock_cursor
        mock_get_connection.return_value = mock_connection
        
        result = test_connection()
        assert result is True
    
    @patch('database.connection.get_connection')
    def test_connection_failure(self, mock_get_connection):
        """Test database connection failure."""
        mock_get_connection.side_effect = Exception("Connection failed")
        
        result = test_connection()
        assert result is False


class TestDatabaseModels:
    """Test database models."""
    
    def test_team_model(self):
        """Test Team model."""
        team = Team(
            id=1,
            name="Test Team",
            code="TT",
            country="Test Country"
        )
        
        assert team.id == 1
        assert team.name == "Test Team"
        assert team.code == "TT"
        assert team.country == "Test Country"
    
    def test_league_model(self):
        """Test League model."""
        league = League(
            id=1,
            name="Test League",
            country="Test Country",
            season=2024
        )
        
        assert league.id == 1
        assert league.name == "Test League"
        assert league.country == "Test Country"
        assert league.season == 2024
    
    def test_match_model(self):
        """Test Match model."""
        from datetime import datetime
        
        match = Match(
            id=1,
            league_id=1,
            season=2024,
            round="Round 1",
            date=datetime.now(),
            home_team_id=1,
            away_team_id=2,
            home_goals=2,
            away_goals=1,
            status=MatchStatus.FINISHED
        )
        
        assert match.id == 1
        assert match.home_goals == 2
        assert match.away_goals == 1
        assert match.is_finished is True
        assert match.total_goals == 3
        assert match.outcome == PredictionOutcome.HOME_WIN
    
    def test_match_outcome_draw(self):
        """Test match outcome for draw."""
        from datetime import datetime
        
        match = Match(
            id=1,
            league_id=1,
            season=2024,
            round="Round 1",
            date=datetime.now(),
            home_team_id=1,
            away_team_id=2,
            home_goals=1,
            away_goals=1,
            status=MatchStatus.FINISHED
        )
        
        assert match.outcome == PredictionOutcome.DRAW
    
    def test_match_outcome_away_win(self):
        """Test match outcome for away win."""
        from datetime import datetime
        
        match = Match(
            id=1,
            league_id=1,
            season=2024,
            round="Round 1",
            date=datetime.now(),
            home_team_id=1,
            away_team_id=2,
            home_goals=1,
            away_goals=2,
            status=MatchStatus.FINISHED
        )
        
        assert match.outcome == PredictionOutcome.AWAY_WIN


class TestDatabaseMigrations:
    """Test database migrations."""
    
    @patch('database.migrations.get_db_cursor')
    def test_setup_database_success(self, mock_cursor):
        """Test successful database setup."""
        mock_cursor.return_value.__enter__.return_value = MagicMock()
        
        result = setup_database()
        assert result is True
    
    @patch('database.migrations.get_db_cursor')
    def test_setup_database_failure(self, mock_cursor):
        """Test database setup failure."""
        mock_cursor.side_effect = Exception("Setup failed")
        
        result = setup_database()
        assert result is False
    
    @patch('database.migrations.get_db_cursor')
    def test_drop_all_tables_success(self, mock_cursor):
        """Test successful table dropping."""
        mock_cursor.return_value.__enter__.return_value = MagicMock()
        
        result = drop_all_tables()
        assert result is True
    
    @patch('database.migrations.get_db_cursor')
    def test_drop_all_tables_failure(self, mock_cursor):
        """Test table dropping failure."""
        mock_cursor.side_effect = Exception("Drop failed")
        
        result = drop_all_tables()
        assert result is False
