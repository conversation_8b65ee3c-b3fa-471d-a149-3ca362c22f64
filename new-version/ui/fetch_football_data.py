"""
All-in-one football data fetching functionality.
Fetches leagues, teams, and matches automatically in one flow.
"""
import logging
from core import DataManager
from api import FootballApiClient
from utils.validators import validate_season

logger = logging.getLogger(__name__)


def fetch_football_data_menu():
    """
    All-in-one football data fetching.
    Automatically fetches leagues, teams, and matches in one flow.
    """
    print("\n" + "=" * 70)
    print("🚀 FETCH FOOTBALL DATA - ALL IN ONE")
    print("=" * 70)
    print("This will automatically fetch leagues, teams, and matches for you!")
    
    try:
        # Initialize API client and data manager
        api_client = FootballApiClient()
        data_manager = DataManager()
        
        # Step 1: Test API connection
        print("\n🔌 Step 1: Testing API connection...")
        if not api_client.test_connection():
            print("❌ API connection failed! Please check your API key and internet connection.")
            return
        print("✅ API connection successful!")
        
        # Step 2: Choose data source
        print("\n📋 Step 2: Select Data Source")
        print("  1. 🌍 Select by Country (National Leagues)")
        print("  2. 🏆 UEFA Champions League")
        print("  3. 🏆 UEFA Europa League")
        print("  4. 🌐 View All Available Competitions")
        print("  0. ❌ Cancel")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == '0':
            print("❌ Operation cancelled.")
            return
        
        # Step 3: Get season (always needed)
        print(f"\n📅 Step 3: Select Season")
        print("Available seasons:")
        
        # Get available seasons from API
        print("🔍 Checking available seasons from API...")
        seasons_data = api_client.get_seasons()
        if seasons_data:
            available_seasons = sorted(seasons_data, reverse=True)[:5]  # Last 5 seasons
            for season in available_seasons:
                print(f"  • {season}")
        else:
            print("  • 2024 (current)")
            print("  • 2023")
            print("  • 2022")
        
        season_input = input("\nEnter season year (e.g., 2024): ").strip()
        season = validate_season(season_input)
        if not season:
            print("❌ Invalid season year.")
            return
        
        # Validate season exists via API
        if seasons_data and season not in seasons_data:
            print(f"⚠️  Warning: Season {season} not found in API data.")
            confirm = input("Continue anyway? (y/n): ").lower()
            if confirm != 'y':
                return
        
        # Step 4: Handle different data sources
        if choice == '1':
            # Country-based leagues
            success = _fetch_by_country(api_client, data_manager, season)
        elif choice == '2':
            # UEFA Champions League
            success = _fetch_uefa_competition(api_client, data_manager, season, "Champions League")
        elif choice == '3':
            # UEFA Europa League
            success = _fetch_uefa_competition(api_client, data_manager, season, "Europa League")
        elif choice == '4':
            # All competitions
            success = _fetch_all_competitions(api_client, data_manager, season)
        else:
            print("❌ Invalid choice.")
            return
        
        if success:
            print("\n" + "=" * 70)
            print("🎉 SUCCESS: FOOTBALL DATA FETCHED SUCCESSFULLY!")
            print("=" * 70)
            print("✅ All data has been fetched and stored in the database")
            print("✅ Leagues, teams, and matches are ready for use")
            print("✅ You can now make predictions or view statistics")
            print("\n💡 Next steps:")
            print("  • Use 'Update Match Results' to get latest scores")
            print("  • Use 'View Database Statistics' to see your data")
            print("  • Train ML models with the fetched data")
        else:
            print("\n" + "=" * 70)
            print("❌ ERROR: FAILED TO FETCH FOOTBALL DATA")
            print("=" * 70)
            print("• The operation could not be completed")
            print("• Please check the logs for detailed error information")
            print("• You can try again or contact support")
            
    except Exception as e:
        logger.error(f"Error in fetch football data: {e}")
        print(f"❌ Unexpected error: {e}")
    
    input("\nPress Enter to continue...")


def _fetch_by_country(api_client, data_manager, season):
    """Fetch data by country selection."""
    print(f"\n🌍 Step 4: Select Country")
    
    # Get countries from API
    print("🔍 Loading countries from API...")
    countries_data = api_client.get_countries()
    
    if not countries_data:
        print("❌ Could not load countries from API.")
        return False
    
    # Show popular countries
    popular_countries = ['England', 'Spain', 'Germany', 'Italy', 'France', 'Netherlands', 'Portugal', 'Brazil']
    available_popular = [c for c in countries_data if c.get('name') in popular_countries]
    
    print("\n📍 Popular Countries:")
    for i, country in enumerate(available_popular[:8], 1):
        print(f"  {i}. {country['name']}")
    
    print(f"\n  9. 🔍 Search all countries")
    print(f"  0. ❌ Cancel")
    
    country_choice = input(f"\nEnter choice (1-9): ").strip()
    
    if country_choice == '0':
        return False
    elif country_choice == '9':
        # Search functionality
        search_term = input("Enter country name to search: ").strip().lower()
        matching_countries = [c for c in countries_data if search_term in c.get('name', '').lower()]
        
        if not matching_countries:
            print(f"❌ No countries found matching '{search_term}'")
            return False
        
        print(f"\n🔍 Countries matching '{search_term}':")
        for i, country in enumerate(matching_countries[:10], 1):
            print(f"  {i}. {country['name']}")
        
        match_choice = input(f"\nSelect country (1-{min(10, len(matching_countries))}): ").strip()
        try:
            selected_country = matching_countries[int(match_choice) - 1]
        except (ValueError, IndexError):
            print("❌ Invalid selection.")
            return False
    else:
        # Popular country selection
        try:
            selected_country = available_popular[int(country_choice) - 1]
        except (ValueError, IndexError):
            print("❌ Invalid selection.")
            return False
    
    print(f"\n✅ Selected: {selected_country['name']}")
    
    # Step 5: Get leagues for country
    print(f"\n🏆 Step 5: Loading leagues for {selected_country['name']}...")
    leagues_data = api_client.get_leagues(country=selected_country['name'], season=season)
    
    if not leagues_data:
        print(f"❌ No leagues found for {selected_country['name']} in season {season}")
        return False
    
    print(f"✅ Found {len(leagues_data)} leagues")
    
    # Show leagues and let user select
    print(f"\n📋 Available Leagues:")
    for i, league in enumerate(leagues_data[:10], 1):
        league_info = league.get('league', {})
        print(f"  {i}. {league_info.get('name')} (ID: {league_info.get('id')})")
    
    if len(leagues_data) > 10:
        print(f"     ... and {len(leagues_data) - 10} more")
    
    league_choice = input(f"\nSelect league (1-{min(10, len(leagues_data))}), or 'all' for all leagues: ").strip().lower()
    
    if league_choice == 'all':
        selected_leagues = leagues_data
    else:
        try:
            selected_leagues = [leagues_data[int(league_choice) - 1]]
        except (ValueError, IndexError):
            print("❌ Invalid selection.")
            return False
    
    # Step 6: Fetch all data for selected leagues
    total_success = True
    for league_data in selected_leagues:
        league_info = league_data.get('league', {})
        league_id = league_info.get('id')
        league_name = league_info.get('name')
        
        print(f"\n🔄 Processing {league_name}...")
        
        # Fetch leagues, teams, and matches automatically
        success = _fetch_complete_league_data(data_manager, league_id, season, selected_country.get('name'))
        if not success:
            total_success = False
            print(f"❌ Failed to fetch data for {league_name}")
        else:
            print(f"✅ Successfully fetched data for {league_name}")
    
    return total_success


def _fetch_uefa_competition(api_client, data_manager, season, competition_type):
    """Fetch UEFA competition data."""
    print(f"\n🏆 Step 4: Loading {competition_type} data...")
    
    # Search for UEFA competition
    leagues_data = api_client.get_leagues(season=season)
    uefa_leagues = [l for l in leagues_data if competition_type.lower() in l.get('league', {}).get('name', '').lower() and 'uefa' in l.get('league', {}).get('name', '').lower()]
    
    if not uefa_leagues:
        print(f"❌ {competition_type} not found for season {season}")
        return False
    
    # Usually there's only one, but show options if multiple
    if len(uefa_leagues) == 1:
        selected_league = uefa_leagues[0]
    else:
        print(f"📋 Found multiple {competition_type} competitions:")
        for i, league in enumerate(uefa_leagues, 1):
            league_info = league.get('league', {})
            print(f"  {i}. {league_info.get('name')}")
        
        choice = input(f"Select competition (1-{len(uefa_leagues)}): ").strip()
        try:
            selected_league = uefa_leagues[int(choice) - 1]
        except (ValueError, IndexError):
            print("❌ Invalid selection.")
            return False
    
    league_info = selected_league.get('league', {})
    league_id = league_info.get('id')
    league_name = league_info.get('name')
    
    print(f"✅ Selected: {league_name}")
    
    # Fetch complete data
    return _fetch_complete_league_data(data_manager, league_id, season, "International")


def _fetch_all_competitions(api_client, data_manager, season):
    """Show all competitions and let user select."""
    print(f"\n🌐 Step 4: Loading all competitions for season {season}...")
    
    leagues_data = api_client.get_leagues(season=season)
    if not leagues_data:
        print(f"❌ No competitions found for season {season}")
        return False
    
    print(f"✅ Found {len(leagues_data)} competitions")
    
    # Group by country for better display
    by_country = {}
    for league in leagues_data:
        league_info = league.get('league', {})
        country = league.get('country', {}).get('name', 'Unknown')
        if country not in by_country:
            by_country[country] = []
        by_country[country].append(league)
    
    # Show first 20 competitions
    print(f"\n📋 Available Competitions (showing first 20):")
    count = 0
    for country, leagues in sorted(by_country.items())[:10]:
        print(f"\n🌍 {country}:")
        for league in leagues[:3]:  # Max 3 per country for display
            count += 1
            if count > 20:
                break
            league_info = league.get('league', {})
            print(f"  {count}. {league_info.get('name')} (ID: {league_info.get('id')})")
        if count > 20:
            break
    
    if len(leagues_data) > 20:
        print(f"\n... and {len(leagues_data) - 20} more competitions")
    
    # Let user search or select
    print(f"\nOptions:")
    print(f"  1-{min(20, len(leagues_data))}. Select by number")
    print(f"  S. Search competitions")
    print(f"  0. Cancel")
    
    choice = input(f"\nEnter choice: ").strip().upper()
    
    if choice == '0':
        return False
    elif choice == 'S':
        search_term = input("Enter league name to search: ").strip().lower()
        matching_leagues = [l for l in leagues_data if search_term in l.get('league', {}).get('name', '').lower()]
        
        if not matching_leagues:
            print(f"❌ No leagues found matching '{search_term}'")
            return False
        
        print(f"\n🔍 Leagues matching '{search_term}':")
        for i, league in enumerate(matching_leagues[:10], 1):
            league_info = league.get('league', {})
            country = league.get('country', {}).get('name', 'Unknown')
            print(f"  {i}. {league_info.get('name')} ({country})")
        
        match_choice = input(f"\nSelect league (1-{min(10, len(matching_leagues))}): ").strip()
        try:
            selected_league = matching_leagues[int(match_choice) - 1]
        except (ValueError, IndexError):
            print("❌ Invalid selection.")
            return False
    else:
        # Direct number selection
        try:
            # Flatten leagues list for selection
            flat_leagues = []
            for country, leagues in sorted(by_country.items()):
                flat_leagues.extend(leagues[:3])
            
            selected_league = flat_leagues[int(choice) - 1]
        except (ValueError, IndexError):
            print("❌ Invalid selection.")
            return False
    
    league_info = selected_league.get('league', {})
    league_id = league_info.get('id')
    league_name = league_info.get('name')
    country = selected_league.get('country', {}).get('name', 'Unknown')
    
    print(f"✅ Selected: {league_name} ({country})")
    
    # Fetch complete data
    return _fetch_complete_league_data(data_manager, league_id, season, country)


def _fetch_complete_league_data(data_manager, league_id, season, country):
    """
    Fetch complete data for a league: league info, teams, and matches.
    """
    try:
        print(f"  🔄 Step 1: Storing league information...")
        league_count = data_manager.fetch_and_store_leagues_by_id(league_id, season, country)
        if league_count == 0:
            print(f"  ❌ Failed to store league information")
            return False
        print(f"  ✅ League information stored")
        
        print(f"  🔄 Step 2: Fetching and storing teams...")
        team_count = data_manager.fetch_and_store_teams(league_id, season)
        if team_count == 0:
            print(f"  ❌ No teams found for this league")
            return False
        print(f"  ✅ Stored {team_count} teams")
        
        print(f"  🔄 Step 3: Fetching and storing matches...")
        result = data_manager.fetch_and_store_matches(league_id, season)
        matches_count = result.get('matches', 0)
        upcoming_count = result.get('upcoming_games', 0)
        total_matches = matches_count + upcoming_count
        
        if total_matches == 0:
            print(f"  ⚠️  No matches found for this league")
        else:
            print(f"  ✅ Stored {total_matches} matches ({matches_count} completed, {upcoming_count} upcoming)")
        
        return True
        
    except Exception as e:
        logger.error(f"Error fetching complete league data: {e}")
        print(f"  ❌ Error: {e}")
        return False
