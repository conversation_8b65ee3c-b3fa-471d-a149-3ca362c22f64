"""
Database statistics functionality.
"""
import logging
from database import get_db_cursor

logger = logging.getLogger(__name__)


def view_database_stats_menu():
    """View database statistics."""
    print("\n" + "=" * 60)
    print("📊 DATABASE STATISTICS")
    print("=" * 60)
    
    try:
        with get_db_cursor() as cursor:
            # Get basic counts
            print("\n📈 Data Overview:")
            
            # Teams count
            cursor.execute("SELECT COUNT(*) FROM teams")
            teams_count = cursor.fetchone()[0]
            print(f"   Teams: {teams_count:,}")
            
            # Leagues count
            cursor.execute("SELECT COUNT(*) FROM leagues")
            leagues_count = cursor.fetchone()[0]
            print(f"   Leagues: {leagues_count:,}")
            
            # Matches count
            cursor.execute("SELECT COUNT(*) FROM matches")
            matches_count = cursor.fetchone()[0]
            print(f"   Completed Matches: {matches_count:,}")
            
            # Upcoming games count
            cursor.execute("SELECT COUNT(*) FROM upcoming_games")
            upcoming_count = cursor.fetchone()[0]
            print(f"   Upcoming Games: {upcoming_count:,}")
            
            # Predictions count (if table exists)
            try:
                cursor.execute("SELECT COUNT(*) FROM predictions")
                predictions_count = cursor.fetchone()[0]
                print(f"   Predictions: {predictions_count:,}")
            except:
                print(f"   Predictions: 0 (table not created yet)")
            
            # Top countries by leagues
            print(f"\n🌍 Top Countries by Leagues:")
            cursor.execute("""
                SELECT country, COUNT(*) as league_count
                FROM leagues
                GROUP BY country
                ORDER BY league_count DESC
                LIMIT 10
            """)
            countries = cursor.fetchall()
            for country in countries:
                print(f"   {country['country']}: {country['league_count']} leagues")
            
            # Recent seasons
            print(f"\n📅 Available Seasons:")
            cursor.execute("""
                SELECT season, COUNT(*) as league_count
                FROM leagues
                GROUP BY season
                ORDER BY season DESC
                LIMIT 5
            """)
            seasons = cursor.fetchall()
            for season in seasons:
                print(f"   {season['season']}: {season['league_count']} leagues")
            
            # Match statistics by status
            if matches_count > 0 or upcoming_count > 0:
                print(f"\n⚽ Match Status Distribution:")
                
                # Completed matches by status
                cursor.execute("""
                    SELECT status, COUNT(*) as count
                    FROM matches
                    GROUP BY status
                    ORDER BY count DESC
                """)
                match_statuses = cursor.fetchall()
                for status in match_statuses:
                    print(f"   {status['status']}: {status['count']} matches")
                
                # Upcoming games by status
                cursor.execute("""
                    SELECT status, COUNT(*) as count
                    FROM upcoming_games
                    GROUP BY status
                    ORDER BY count DESC
                """)
                upcoming_statuses = cursor.fetchall()
                for status in upcoming_statuses:
                    print(f"   {status['status']} (upcoming): {status['count']} games")
            
            # Data freshness
            print(f"\n🕒 Data Freshness:")
            
            # Most recent match
            cursor.execute("""
                SELECT MAX(date) as latest_match
                FROM matches
                WHERE date IS NOT NULL
            """)
            latest_match = cursor.fetchone()
            if latest_match and latest_match['latest_match']:
                print(f"   Latest completed match: {latest_match['latest_match']}")
            
            # Most recent upcoming game
            cursor.execute("""
                SELECT MIN(date) as next_game
                FROM upcoming_games
                WHERE date > NOW()
            """)
            next_game = cursor.fetchone()
            if next_game and next_game['next_game']:
                print(f"   Next upcoming game: {next_game['next_game']}")
            
            # Database size (approximate)
            print(f"\n💾 Storage Information:")
            cursor.execute("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
            """)
            table_sizes = cursor.fetchall()
            for table in table_sizes:
                print(f"   {table['tablename']}: {table['size']}")
            
    except Exception as e:
        logger.error(f"Error getting database statistics: {e}")
        print(f"❌ Error: {e}")
    
    input("\nPress Enter to continue...")
