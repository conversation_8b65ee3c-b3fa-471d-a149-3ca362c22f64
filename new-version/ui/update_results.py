"""
Update match results functionality.
"""
import logging
from core import DataManager

logger = logging.getLogger(__name__)


def update_match_results_menu():
    """Update match results from API."""
    print("\n" + "=" * 60)
    print("🔄 UPDATE MATCH RESULTS")
    print("=" * 60)

    try:
        # Initialize data manager
        data_manager = DataManager()

        # Test API connection
        print("\n🔌 Testing API connection...")
        if not data_manager.test_api_connection():
            print("❌ API connection failed! Please check your API key and internet connection.")
            return
        print("✅ API connection successful!")

        print("\n📋 This will:")
        print("  1. Check upcoming games for completed matches")
        print("  2. Update scores and match status")
        print("  3. Move completed matches to matches table")
        print("  4. Update any predictions with actual results")

        confirm = input(f"\n✓ Continue with updating match results? (y/n): ").lower()
        if confirm != 'y':
            print("❌ Operation cancelled.")
            return

        # Step 1: Update match results from API
        print(f"\n🔄 Step 1: Updating match results from API...")
        updated_count = data_manager.update_match_results()
        print(f"✅ Updated {updated_count} matches with latest results")

        # Step 2: Move completed games to matches table
        print(f"\n🔄 Step 2: Moving completed games to matches table...")
        moved_count = data_manager.move_completed_games_to_matches()
        print(f"✅ Moved {moved_count} completed games to matches table")

        # Step 3: Update match statistics
        print(f"\n🔄 Step 3: Fetching detailed match statistics...")
        stats_updated = update_match_statistics()
        print(f"✅ Updated statistics for {stats_updated} matches")

        # Step 3b: Retry missing statistics with slow approach
        print(f"\n🔄 Step 3b: Retrying missing statistics with slower approach...")
        retry_updated = retry_missing_statistics_slow()
        print(f"✅ Recovered {retry_updated} additional matches on retry")

        # Step 4: Update predictions with actual results
        print(f"\n🔄 Step 4: Updating predictions with actual results...")
        predictions_updated = update_predictions_with_results()
        print(f"✅ Updated {predictions_updated} predictions with actual results")

        print(f"\n✅ Match results update completed!")
        print(f"   • {updated_count} matches updated from API")
        print(f"   • {moved_count} completed games moved to matches table")
        print(f"   • {stats_updated} matches updated with statistics")
        print(f"   • {predictions_updated} predictions updated with results")

        if updated_count > 0 or moved_count > 0 or stats_updated > 0:
            print(f"\n💡 Tip: You can now train ML models with the updated match data!")

        if predictions_updated > 0:
            print(f"💡 Tip: Check prediction accuracy using 'Prediction Management' → 'Prediction Accuracy Analysis'!")

    except Exception as e:
        logger.error(f"Error updating match results: {e}")
        print(f"❌ Error: {e}")

    input("\nPress Enter to continue...")


def update_match_statistics() -> int:
    """
    Update statistics for matches that don't have detailed statistics yet.

    Returns:
        Number of matches updated with statistics
    """
    try:
        from database import get_db_cursor
        from database.models import Match

        data_manager = DataManager()

        # Get matches without complete statistics
        with get_db_cursor() as cursor:
            cursor.execute("""
                SELECT id, home_team_id, away_team_id
                FROM matches
                WHERE (home_shots IS NULL OR home_fouls IS NULL OR home_yellow_cards IS NULL)
                AND status IN ('FT', 'AET', 'PEN')
                ORDER BY date DESC
                LIMIT 200
            """)
            matches_to_update = cursor.fetchall()

        if not matches_to_update:
            logger.info("No matches need statistics updates")
            return 0

        # Use the optimized parallel processing from DataManager
        print(f"    🚀 Using optimized parallel processing for {len(matches_to_update)} matches...")

        # Convert to Match objects for the optimized function
        from database.models import Match
        matches = []
        for match_data in matches_to_update:
            match = Match(
                id=match_data['id'],
                league_id=0,  # Not needed for stats update
                season=0,     # Not needed for stats update
                round="",     # Not needed for stats update
                date=None,    # Not needed for stats update
                home_team_id=match_data['home_team_id'],
                away_team_id=match_data['away_team_id']
            )
            matches.append(match)

        # Use the optimized function
        data_manager._fetch_and_store_match_statistics_optimized(matches)

        # Count successful updates by checking how many now have statistics
        with get_db_cursor() as cursor:
            match_ids = [m['id'] for m in matches_to_update]
            placeholders = ','.join(['%s'] * len(match_ids))
            cursor.execute(f"""
                SELECT COUNT(*) as updated_count
                FROM matches
                WHERE id IN ({placeholders})
                AND home_shots IS NOT NULL
                AND home_fouls IS NOT NULL
                AND home_yellow_cards IS NOT NULL
            """, match_ids)
            updated_count = cursor.fetchone()['updated_count']

        logger.info(f"Updated statistics for {updated_count} matches")
        return updated_count

    except Exception as e:
        logger.error(f"Failed to update match statistics: {e}")
        return 0


def retry_missing_statistics_slow() -> int:
    """
    Retry fetching statistics for matches that failed with a slower, more careful approach.

    Returns:
        Number of matches successfully recovered
    """
    try:
        from core import DataManager

        data_manager = DataManager()

        # Use the slow retry function from DataManager
        retry_count = data_manager._retry_missing_statistics_slow()

        logger.info(f"Retry recovered {retry_count} matches")
        return retry_count

    except Exception as e:
        logger.error(f"Failed to retry missing statistics: {e}")
        return 0


def update_predictions_with_results() -> int:
    """
    Update predictions with actual match results.

    Returns:
        Number of predictions updated
    """
    try:
        from database import get_db_cursor
        from database.models import PredictionOutcome

        # Get predictions that need actual results
        with get_db_cursor() as cursor:
            cursor.execute("""
                SELECT
                    p.id,
                    p.match_id,
                    p.predicted_outcome,
                    p.predicted_home_goals,
                    p.predicted_away_goals,
                    m.home_goals,
                    m.away_goals
                FROM predictions p
                JOIN matches m ON p.match_id = m.id
                WHERE p.actual_outcome IS NULL
                AND m.home_goals IS NOT NULL
                AND m.away_goals IS NOT NULL
                AND m.status IN ('FT', 'AET', 'PEN')
            """)

            predictions_to_update = cursor.fetchall()

        if not predictions_to_update:
            logger.info("No predictions need result updates")
            return 0

        updated_count = 0

        for pred in predictions_to_update:
            try:
                # Determine actual outcome
                home_goals = pred['home_goals']
                away_goals = pred['away_goals']

                if home_goals > away_goals:
                    actual_outcome = PredictionOutcome.HOME_WIN.value
                elif home_goals < away_goals:
                    actual_outcome = PredictionOutcome.AWAY_WIN.value
                else:
                    actual_outcome = PredictionOutcome.DRAW.value

                total_goals = home_goals + away_goals

                # Calculate accuracy metrics
                outcome_accuracy = 1.0 if pred['predicted_outcome'] == actual_outcome else 0.0

                # Goals accuracy (using mean absolute error, converted to accuracy)
                home_error = abs(pred['predicted_home_goals'] - home_goals)
                away_error = abs(pred['predicted_away_goals'] - away_goals)
                max_error = max(home_error, away_error, 1.0)
                goals_accuracy = max(0.0, 1.0 - (home_error + away_error) / (2 * max_error))

                # Total accuracy (weighted combination)
                total_accuracy = 0.6 * outcome_accuracy + 0.4 * goals_accuracy

                # Update prediction
                with get_db_cursor() as cursor:
                    cursor.execute("""
                        UPDATE predictions SET
                            actual_outcome = %s,
                            actual_home_goals = %s,
                            actual_away_goals = %s,
                            actual_total_goals = %s,
                            outcome_accuracy = %s,
                            goals_accuracy = %s,
                            total_accuracy = %s,
                            updated_at = NOW()
                        WHERE id = %s
                    """, (
                        actual_outcome,
                        home_goals,
                        away_goals,
                        total_goals,
                        outcome_accuracy,
                        goals_accuracy,
                        total_accuracy,
                        pred['id']
                    ))

                updated_count += 1

            except Exception as e:
                logger.error(f"Failed to update prediction {pred['id']}: {e}")
                continue

        logger.info(f"Updated {updated_count} predictions with actual results")
        return updated_count

    except Exception as e:
        logger.error(f"Failed to update predictions with results: {e}")
        return 0
