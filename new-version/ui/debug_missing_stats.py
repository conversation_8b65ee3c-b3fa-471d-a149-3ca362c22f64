"""
Debug utility to investigate missing statistics.
"""
import logging
from core import DataManager
from database import get_db_cursor

logger = logging.getLogger(__name__)


def debug_missing_stats_menu():
    """Debug missing statistics for specific matches."""
    print("\n" + "=" * 60)
    print("🔍 DEBUG MISSING STATISTICS")
    print("=" * 60)
    
    try:
        # Temporarily increase logging level to DEBUG
        root_logger = logging.getLogger()
        original_level = root_logger.level
        root_logger.setLevel(logging.DEBUG)
        
        # Also set specific loggers to DEBUG
        logging.getLogger('core.data_manager').setLevel(logging.DEBUG)
        logging.getLogger('api.football_api').setLevel(logging.DEBUG)
        
        print("\n🔍 This will help investigate why some matches have no statistics")
        print("   Debug logging is temporarily enabled for detailed analysis")
        
        # Get some matches without statistics
        with get_db_cursor() as cursor:
            cursor.execute("""
                SELECT id, date, status, home_goals, away_goals, round, home_team_id, away_team_id
                FROM matches 
                WHERE (home_shots IS NULL OR home_fouls IS NULL OR home_yellow_cards IS NULL)
                AND status IN ('FT', 'AET', 'PEN')
                ORDER BY date DESC
                LIMIT 5
            """)
            matches_to_debug = cursor.fetchall()
        
        if not matches_to_debug:
            print("✅ No matches found that are missing statistics!")
            return
        
        print(f"\n📋 Found {len(matches_to_debug)} matches missing statistics:")
        for i, match in enumerate(matches_to_debug, 1):
            print(f"  {i}. Match {match['id']} - {match['date']} - Status: {match['status']} - Score: {match['home_goals']}-{match['away_goals']}")
        
        choice = input(f"\nSelect match to debug (1-{len(matches_to_debug)}), or 'all' for all: ").strip().lower()
        
        data_manager = DataManager()
        
        if choice == 'all':
            selected_matches = matches_to_debug
        else:
            try:
                selected_matches = [matches_to_debug[int(choice) - 1]]
            except (ValueError, IndexError):
                print("❌ Invalid selection.")
                return
        
        print(f"\n🔍 Debugging {len(selected_matches)} matches...")
        
        for match in selected_matches:
            match_id = match['id']
            home_team_id = match['home_team_id']
            away_team_id = match['away_team_id']
            
            print(f"\n" + "=" * 50)
            print(f"🔍 DEBUGGING MATCH {match_id}")
            print(f"   Date: {match['date']}")
            print(f"   Status: {match['status']}")
            print(f"   Score: {match['home_goals']}-{match['away_goals']}")
            print(f"   Round: {match['round']}")
            print("=" * 50)
            
            # Test API calls individually
            print(f"\n1️⃣ Testing home team statistics (team {home_team_id})...")
            home_stats = data_manager.api_client.get_fixture_statistics(match_id, home_team_id)
            print(f"   Response: {len(home_stats) if home_stats else 0} items")
            if home_stats:
                print(f"   Sample: {home_stats[0] if home_stats else 'None'}")
            
            print(f"\n2️⃣ Testing away team statistics (team {away_team_id})...")
            away_stats = data_manager.api_client.get_fixture_statistics(match_id, away_team_id)
            print(f"   Response: {len(away_stats) if away_stats else 0} items")
            if away_stats:
                print(f"   Sample: {away_stats[0] if away_stats else 'None'}")
            
            # Test without team parameter
            print(f"\n3️⃣ Testing fixture statistics without team parameter...")
            all_stats = data_manager.api_client.get_fixture_statistics(match_id)
            print(f"   Response: {len(all_stats) if all_stats else 0} items")
            if all_stats:
                print(f"   Sample: {all_stats[0] if all_stats else 'None'}")
            
            # Investigate possible reasons
            print(f"\n4️⃣ Investigating possible reasons...")
            data_manager._investigate_missing_stats(match_id)
            
            # Test parsing if we have any data
            if home_stats and away_stats:
                print(f"\n5️⃣ Testing statistics parsing...")
                try:
                    home_parsed = data_manager._parse_team_statistics(home_stats)
                    away_parsed = data_manager._parse_team_statistics(away_stats)
                    print(f"   Home parsed: {home_parsed}")
                    print(f"   Away parsed: {away_parsed}")
                except Exception as e:
                    print(f"   ❌ Parsing error: {e}")
            
            input(f"\nPress Enter to continue to next match...")
        
        print(f"\n✅ Debug analysis completed!")
        print(f"   Check the logs above for detailed information about why statistics are missing")
        
    except Exception as e:
        logger.error(f"Error in debug analysis: {e}")
        print(f"❌ Error: {e}")
    
    finally:
        # Restore original logging level
        root_logger.setLevel(original_level)
        logging.getLogger('core.data_manager').setLevel(original_level)
        logging.getLogger('api.football_api').setLevel(original_level)
    
    input("\nPress Enter to continue...")


def analyze_statistics_coverage():
    """Analyze statistics coverage across all matches."""
    print("\n" + "=" * 60)
    print("📊 STATISTICS COVERAGE ANALYSIS")
    print("=" * 60)
    
    try:
        with get_db_cursor() as cursor:
            # Get overall statistics
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_matches,
                    COUNT(home_shots) as matches_with_shots,
                    COUNT(home_fouls) as matches_with_fouls,
                    COUNT(home_yellow_cards) as matches_with_cards,
                    COUNT(home_possession) as matches_with_possession
                FROM matches 
                WHERE status IN ('FT', 'AET', 'PEN')
            """)
            overall_stats = cursor.fetchone()
            
            total = overall_stats['total_matches']
            shots_coverage = (overall_stats['matches_with_shots'] / total * 100) if total > 0 else 0
            fouls_coverage = (overall_stats['matches_with_fouls'] / total * 100) if total > 0 else 0
            cards_coverage = (overall_stats['matches_with_cards'] / total * 100) if total > 0 else 0
            possession_coverage = (overall_stats['matches_with_possession'] / total * 100) if total > 0 else 0
            
            print(f"\n📈 Overall Statistics Coverage:")
            print(f"   Total finished matches: {total:,}")
            print(f"   Shots statistics: {overall_stats['matches_with_shots']:,} ({shots_coverage:.1f}%)")
            print(f"   Fouls statistics: {overall_stats['matches_with_fouls']:,} ({fouls_coverage:.1f}%)")
            print(f"   Cards statistics: {overall_stats['matches_with_cards']:,} ({cards_coverage:.1f}%)")
            print(f"   Possession statistics: {overall_stats['matches_with_possession']:,} ({possession_coverage:.1f}%)")
            
            # Get coverage by year
            cursor.execute("""
                SELECT 
                    EXTRACT(YEAR FROM date) as year,
                    COUNT(*) as total_matches,
                    COUNT(home_shots) as matches_with_stats
                FROM matches 
                WHERE status IN ('FT', 'AET', 'PEN')
                AND date IS NOT NULL
                GROUP BY EXTRACT(YEAR FROM date)
                ORDER BY year DESC
                LIMIT 5
            """)
            yearly_stats = cursor.fetchall()
            
            print(f"\n📅 Statistics Coverage by Year:")
            for year_stat in yearly_stats:
                year = int(year_stat['year'])
                total_year = year_stat['total_matches']
                with_stats = year_stat['matches_with_stats']
                coverage = (with_stats / total_year * 100) if total_year > 0 else 0
                print(f"   {year}: {with_stats:,}/{total_year:,} matches ({coverage:.1f}%)")
            
            # Get matches without statistics by status
            cursor.execute("""
                SELECT 
                    status,
                    COUNT(*) as count
                FROM matches 
                WHERE home_shots IS NULL
                GROUP BY status
                ORDER BY count DESC
            """)
            missing_by_status = cursor.fetchall()
            
            if missing_by_status:
                print(f"\n⚠️  Matches Missing Statistics by Status:")
                for status_stat in missing_by_status:
                    print(f"   {status_stat['status']}: {status_stat['count']:,} matches")
            
    except Exception as e:
        logger.error(f"Error in coverage analysis: {e}")
        print(f"❌ Error: {e}")
    
    input("\nPress Enter to continue...")


if __name__ == "__main__":
    debug_missing_stats_menu()
