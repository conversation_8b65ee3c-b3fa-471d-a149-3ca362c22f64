"""
Fetch upcoming games functionality.
"""
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

from core import DataManager
from database import get_db_cursor
from api import FootballApiClient

logger = logging.getLogger(__name__)


def fetch_upcoming_games_menu():
    """Menu for fetching upcoming games."""
    print("\n" + "=" * 60)
    print("📅 FETCH UPCOMING GAMES")
    print("=" * 60)

    try:
        data_manager = DataManager()

        print("\n🔍 Step 1: Select League")

        # Get leagues that actually have data (teams via matches) in database
        with get_db_cursor() as cursor:
            cursor.execute("""
                SELECT DISTINCT l.id, l.name, l.country, l.season,
                       COUNT(DISTINCT m.home_team_id) + COUNT(DISTINCT m.away_team_id) as team_count,
                       COUNT(DISTINCT m.id) as match_count,
                       MAX(m.date) as latest_match_date
                FROM leagues l
                LEFT JOIN matches m ON m.league_id = l.id AND m.season = l.season
                GROUP BY l.id, l.name, l.country, l.season
                HAVING (COUNT(DISTINCT m.home_team_id) + COUNT(DISTINCT m.away_team_id)) >= 10  -- At least 10 teams involved
                AND (COUNT(DISTINCT m.id) > 0 OR l.season >= EXTRACT(YEAR FROM NOW()))  -- Has matches or current/future season
                ORDER BY l.country, l.name, l.season DESC
            """)
            available_leagues = cursor.fetchall()

        if not available_leagues:
            print("❌ No leagues found in database. Please fetch league data first.")
            return

        # Group leagues by country and name, keeping only the latest season with most data
        leagues_grouped = {}
        for league in available_leagues:
            key = f"{league['country']}: {league['name']}"
            if key not in leagues_grouped:
                leagues_grouped[key] = league
            else:
                # Keep the season with more recent data or more matches
                current = leagues_grouped[key]
                if (league['season'] > current['season'] or
                    (league['season'] == current['season'] and league['match_count'] > current['match_count'])):
                    leagues_grouped[key] = league

        # Display leagues with data info
        league_options = []
        print("Available leagues with data:")
        for i, (league_name, league_data) in enumerate(leagues_grouped.items(), 1):
            latest_match = league_data['latest_match_date']
            latest_str = latest_match.strftime('%Y-%m-%d') if latest_match else 'No matches yet'
            print(f"  {i}. {league_name}")
            print(f"     Season: {league_data['season']} | Teams: {league_data['team_count']} | Matches: {league_data['match_count']} | Latest: {latest_str}")
            league_options.append((league_name, league_data))

        choice = input(f"\nSelect league (1-{len(league_options)}): ").strip()
        try:
            selected_index = int(choice) - 1
            if selected_index < 0 or selected_index >= len(league_options):
                raise ValueError()
            selected_league_name, selected_league_data = league_options[selected_index]
        except (ValueError, IndexError):
            print("❌ Invalid selection.")
            return

        print(f"\n📅 Step 2: Confirm Season for {selected_league_name}")
        print(f"🎯 Using season: {selected_league_data['season']}")
        print(f"   Teams in database: {selected_league_data['team_count']}")
        print(f"   Historical matches: {selected_league_data['match_count']}")

        # Use the selected league data directly (already the best season)
        selected_season = selected_league_data

        print(f"\n⏰ Step 3: Select Time Range")
        print("How many days ahead do you want to fetch?")
        print("  1. Next 5 days")
        print("  2. Next 10 days")
        print("  3. Next 14 days")
        print("  4. Next 30 days")
        print("  5. Custom range")

        time_choice = input("\nSelect option (1-5, default: 2): ").strip()

        if time_choice == '1':
            days_ahead = 5
        elif time_choice == '3':
            days_ahead = 14
        elif time_choice == '4':
            days_ahead = 30
        elif time_choice == '5':
            try:
                days_ahead = int(input("Enter number of days ahead: ").strip())
                if days_ahead < 1 or days_ahead > 90:
                    print("❌ Days ahead must be between 1 and 90.")
                    return
            except ValueError:
                print("❌ Invalid number.")
                return
        else:
            days_ahead = 10  # Default

        print(f"\n🚀 Fetching upcoming games...")
        print(f"   League: {selected_league_name}")
        print(f"   Season: {selected_season['season']}")
        print(f"   Time range: Next {days_ahead} days")

        # Validate that we have teams for this league/season (via matches)
        with get_db_cursor() as cursor:
            cursor.execute("""
                SELECT COUNT(DISTINCT home_team_id) + COUNT(DISTINCT away_team_id) as team_count
                FROM matches
                WHERE league_id = %s AND season = %s
            """, (selected_season['id'], selected_season['season']))

            team_check = cursor.fetchone()
            if not team_check or team_check['team_count'] < 4:  # At least 4 unique teams (2 home + 2 away minimum)
                print(f"❌ Not enough teams found for this league/season.")
                print(f"   Please fetch team data first using 'Fetch Football Data'.")
                return

        # Fetch upcoming games
        result = fetch_upcoming_games_for_league(
            league_id=selected_season['id'],
            season=selected_season['season'],
            days_ahead=days_ahead
        )

        if result['success']:
            print(f"\n✅ Successfully fetched upcoming games!")
            print(f"   • {result['upcoming_games']} upcoming games stored")
            print(f"   • Date range: {result['date_from']} to {result['date_to']}")

            # Show summary of fetched games
            show_upcoming_games_summary(selected_season['id'], selected_season['season'])
        else:
            print(f"\n❌ Failed to fetch upcoming games: {result['error']}")

    except Exception as e:
        logger.error(f"Error in fetch upcoming games menu: {e}")
        print(f"❌ Error: {e}")

    input("\nPress Enter to continue...")


def detect_current_season(available_seasons: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
    """
    Auto-detect the current season based on current date.

    Args:
        available_seasons: List of available seasons for the league

    Returns:
        Current season data or None if not found
    """
    current_year = datetime.now().year
    current_month = datetime.now().month

    # Most football seasons start in August/September and end in May/June
    # If we're in the second half of the year (July+), look for current_year+1 season
    # If we're in the first half (Jan-June), look for current_year season

    if current_month >= 7:  # July or later - new season starting
        target_seasons = [current_year + 1, current_year]
    else:  # January to June - current season ongoing
        target_seasons = [current_year, current_year - 1]

    # Find the best matching season
    for target_season in target_seasons:
        for season_data in available_seasons:
            if season_data['season'] == target_season:
                return season_data

    # If no exact match, return the most recent season
    if available_seasons:
        return max(available_seasons, key=lambda x: x['season'])

    return None


def fetch_upcoming_games_for_league(
    league_id: int,
    season: int,
    days_ahead: int = 10
) -> Dict[str, Any]:
    """
    Fetch upcoming games for a specific league and season.

    Args:
        league_id: League ID
        season: Season year
        days_ahead: Number of days ahead to fetch

    Returns:
        Dictionary with result information
    """
    try:
        data_manager = DataManager()

        # Calculate date range
        today = datetime.now().date()
        date_from = today
        date_to = today + timedelta(days=days_ahead)

        logger.info(f"Fetching upcoming games for league {league_id}, season {season}")
        logger.info(f"Date range: {date_from} to {date_to}")

        # Fetch fixtures from API
        result = data_manager.fetch_and_store_matches(
            league_id=league_id,
            season=season,
            date_from=date_from,  # Pass as date object
            date_to=date_to       # Pass as date object
        )

        return {
            'success': True,
            'upcoming_games': result['upcoming_games'],
            'completed_matches': result['matches'],
            'date_from': date_from.strftime('%Y-%m-%d'),
            'date_to': date_to.strftime('%Y-%m-%d')
        }

    except Exception as e:
        logger.error(f"Failed to fetch upcoming games: {e}")
        return {
            'success': False,
            'error': str(e),
            'upcoming_games': 0,
            'completed_matches': 0
        }


def show_upcoming_games_summary(league_id: int, season: int):
    """
    Show summary of upcoming games for a league.

    Args:
        league_id: League ID
        season: Season year
    """
    try:
        with get_db_cursor() as cursor:
            # Get upcoming games count by date
            cursor.execute("""
                SELECT DATE(date) as game_date, COUNT(*) as game_count
                FROM upcoming_games
                WHERE league_id = %s AND season = %s
                AND date >= NOW()
                GROUP BY DATE(date)
                ORDER BY game_date
                LIMIT 10
            """, (league_id, season))

            games_by_date = cursor.fetchall()

            if games_by_date:
                print(f"\n📊 Upcoming Games Schedule:")
                for row in games_by_date:
                    # Extract values using column names
                    game_date_value = row['game_date']
                    count = row['game_count']

                    # Format the date (we know from testing it's a datetime.date object)
                    try:
                        day_name = game_date_value.strftime('%A')
                        date_str = game_date_value.strftime('%Y-%m-%d')
                    except Exception as e:
                        # Fallback if something goes wrong
                        logger.error(f"Date formatting error: {e}, value: {game_date_value}, type: {type(game_date_value)}")
                        day_name = "Unknown"
                        date_str = str(game_date_value)

                    print(f"   • {date_str} ({day_name}): {count} games")

            # Get total count
            cursor.execute("""
                SELECT COUNT(*) as total_count
                FROM upcoming_games
                WHERE league_id = %s AND season = %s
                AND date >= NOW()
            """, (league_id, season))

            total_result = cursor.fetchone()
            total_count = total_result['total_count'] if total_result else 0

            print(f"\n📈 Total upcoming games in database: {total_count}")

    except Exception as e:
        logger.error(f"Error showing upcoming games summary: {e}")


if __name__ == "__main__":
    fetch_upcoming_games_menu()
