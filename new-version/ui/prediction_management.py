"""
Prediction management UI for making and viewing predictions.
"""
import logging
from ml import FootballPredictor
from database import get_db_cursor

logger = logging.getLogger(__name__)


def prediction_management_menu():
    """Prediction management submenu."""
    print("\n" + "=" * 60)
    print("🎯 PREDICTION MANAGEMENT")
    print("=" * 60)
    print()
    print("Available options:")
    print("  1. 🔮 Predict Upcoming Games")
    print("  2. 📊 View Recent Predictions")
    print("  3. 📈 Prediction Accuracy Analysis")
    print("  4. 🎲 Make Single Prediction")
    print("  0. Back to Main Menu")
    print()

    choice = input("Enter your choice: ").strip()

    if choice == '1':
        predict_upcoming_games_menu()
    elif choice == '2':
        view_recent_predictions_menu()
    elif choice == '3':
        prediction_accuracy_menu()
    elif choice == '4':
        single_prediction_menu()
    elif choice == '0':
        return
    else:
        print("❌ Invalid choice.")

    input("\nPress Enter to continue...")


def predict_upcoming_games_menu():
    """Predict upcoming games interface."""
    print("\n" + "=" * 50)
    print("🔮 PREDICT UPCOMING GAMES")
    print("=" * 50)
    
    try:
        predictor = FootballPredictor()
        
        # Check if model is available
        if not predictor.load_model():
            print("\n❌ No trained model available.")
            print("   Please train a model first using 'Model Management' → 'Model Training'.")
            return
        
        print("\n✅ Model loaded successfully!")
        
        # Get upcoming games without predictions
        games = predictor.get_upcoming_games_without_predictions()
        
        if not games:
            print("\n📋 No upcoming games need predictions.")
            print("   All upcoming games already have predictions, or no upcoming games found.")
            return
        
        print(f"\n📋 Found {len(games)} upcoming games without predictions:")
        print()
        
        # Show first few games
        for i, game in enumerate(games[:5], 1):
            print(f"{i}. {game['home_team_name']} vs {game['away_team_name']}")
            print(f"   • League: {game['league_name']}")
            print(f"   • Date: {game['date']}")
            print()
        
        if len(games) > 5:
            print(f"   ... and {len(games) - 5} more games")
            print()
        
        # Confirm prediction
        confirm = input(f"✓ Make predictions for all {len(games)} games? (y/n): ").lower()
        if confirm != 'y':
            print("❌ Prediction cancelled.")
            return
        
        # Make predictions
        print(f"\n🚀 Making predictions for {len(games)} games...")
        print("   This may take a moment...")
        print()
        
        result = predictor.predict_upcoming_games()
        
        if result['success']:
            print("✅ Prediction batch completed!")
            print()
            print("📊 Results:")
            print(f"   • Total games: {result['total_games']}")
            print(f"   • Predictions made: {result['predictions_made']}")
            print(f"   • Failed predictions: {result['failed_predictions']}")
            
            if result['predictions_made'] > 0:
                print()
                print("💡 You can now view predictions using 'View Recent Predictions'.")
        else:
            print(f"❌ Prediction batch failed: {result['error']}")
            
    except Exception as e:
        logger.error(f"Error in predict upcoming games menu: {e}")
        print(f"❌ Error: {e}")


def view_recent_predictions_menu():
    """View recent predictions interface."""
    print("\n" + "=" * 50)
    print("📊 RECENT PREDICTIONS")
    print("=" * 50)
    
    try:
        with get_db_cursor() as cursor:
            # Get recent predictions
            cursor.execute("""
                SELECT 
                    p.match_id,
                    p.predicted_outcome,
                    p.predicted_home_goals,
                    p.predicted_away_goals,
                    p.confidence_home_win,
                    p.confidence_draw,
                    p.confidence_away_win,
                    p.prediction_date,
                    ug.date as match_date,
                    ht.name as home_team_name,
                    at.name as away_team_name,
                    l.name as league_name,
                    p.actual_outcome,
                    p.actual_home_goals,
                    p.actual_away_goals
                FROM predictions p
                LEFT JOIN upcoming_games ug ON p.match_id = ug.id
                LEFT JOIN matches m ON p.match_id = m.id
                LEFT JOIN teams ht ON COALESCE(ug.home_team_id, m.home_team_id) = ht.id
                LEFT JOIN teams at ON COALESCE(ug.away_team_id, m.away_team_id) = at.id
                LEFT JOIN leagues l ON COALESCE(ug.league_id, m.league_id) = l.id 
                    AND COALESCE(ug.season, m.season) = l.season
                ORDER BY p.prediction_date DESC
                LIMIT 20
            """)
            
            predictions = cursor.fetchall()
        
        if not predictions:
            print("\n❌ No predictions found.")
            print("   Make some predictions first using 'Predict Upcoming Games'.")
            return
        
        print(f"\n📊 Recent {len(predictions)} predictions:")
        print()
        
        outcome_names = {1: "Home Win", 0: "Draw", 2: "Away Win"}
        
        for i, pred in enumerate(predictions, 1):
            # Determine if match is completed
            is_completed = pred['actual_outcome'] is not None
            
            print(f"{i}. {pred['home_team_name']} vs {pred['away_team_name']}")
            print(f"   • League: {pred['league_name']}")
            print(f"   • Match Date: {pred['match_date']}")
            print(f"   • Prediction Date: {pred['prediction_date']}")
            print()
            
            # Prediction
            predicted_outcome = outcome_names.get(pred['predicted_outcome'], 'Unknown')
            print(f"   🔮 Prediction: {predicted_outcome}")
            print(f"   • Score: {pred['predicted_home_goals']:.1f} - {pred['predicted_away_goals']:.1f}")
            print(f"   • Confidence: H:{pred['confidence_home_win']:.2f} D:{pred['confidence_draw']:.2f} A:{pred['confidence_away_win']:.2f}")
            
            # Actual result if available
            if is_completed:
                actual_outcome = outcome_names.get(pred['actual_outcome'], 'Unknown')
                is_correct = pred['predicted_outcome'] == pred['actual_outcome']
                status = "✅ Correct" if is_correct else "❌ Incorrect"
                
                print(f"   🏆 Actual: {actual_outcome} ({status})")
                print(f"   • Score: {pred['actual_home_goals']} - {pred['actual_away_goals']}")
            else:
                print(f"   ⏳ Match not completed yet")
            
            print()
            
    except Exception as e:
        logger.error(f"Error viewing recent predictions: {e}")
        print(f"❌ Error: {e}")


def prediction_accuracy_menu():
    """Prediction accuracy analysis interface."""
    print("\n" + "=" * 50)
    print("📈 PREDICTION ACCURACY ANALYSIS")
    print("=" * 50)
    
    try:
        with get_db_cursor() as cursor:
            # Get accuracy statistics
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_predictions,
                    COUNT(CASE WHEN predicted_outcome = actual_outcome THEN 1 END) as correct_outcomes,
                    AVG(CASE WHEN predicted_outcome = actual_outcome THEN 1.0 ELSE 0.0 END) as outcome_accuracy,
                    AVG(ABS(predicted_home_goals - actual_home_goals)) as avg_home_goals_error,
                    AVG(ABS(predicted_away_goals - actual_away_goals)) as avg_away_goals_error,
                    COUNT(CASE WHEN 
                        ABS(predicted_home_goals - actual_home_goals) <= 1.0 AND 
                        ABS(predicted_away_goals - actual_away_goals) <= 1.0 
                        THEN 1 END) as goals_within_1,
                    model_version
                FROM predictions
                WHERE actual_outcome IS NOT NULL
                AND actual_home_goals IS NOT NULL
                AND actual_away_goals IS NOT NULL
                GROUP BY model_version
                ORDER BY COUNT(*) DESC
            """)
            
            accuracy_stats = cursor.fetchall()
        
        if not accuracy_stats:
            print("\n❌ No completed predictions found for accuracy analysis.")
            print("   Predictions need actual match results to calculate accuracy.")
            return
        
        print("\n📈 Accuracy Analysis by Model:")
        print()
        
        for stats in accuracy_stats:
            goals_accuracy = stats['goals_within_1'] / stats['total_predictions'] if stats['total_predictions'] > 0 else 0
            
            print(f"🤖 Model: {stats['model_version']}")
            print(f"   • Total Predictions: {stats['total_predictions']}")
            print(f"   • Outcome Accuracy: {stats['outcome_accuracy']:.3f} ({stats['correct_outcomes']}/{stats['total_predictions']})")
            print(f"   • Goals Accuracy (±1): {goals_accuracy:.3f} ({stats['goals_within_1']}/{stats['total_predictions']})")
            print(f"   • Avg Home Goals Error: {stats['avg_home_goals_error']:.2f}")
            print(f"   • Avg Away Goals Error: {stats['avg_away_goals_error']:.2f}")
            print()
        
        # Recent performance
        print("📊 Recent Performance (Last 30 days):")
        
        with get_db_cursor() as cursor:
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_predictions,
                    AVG(CASE WHEN predicted_outcome = actual_outcome THEN 1.0 ELSE 0.0 END) as outcome_accuracy,
                    COUNT(CASE WHEN 
                        ABS(predicted_home_goals - actual_home_goals) <= 1.0 AND 
                        ABS(predicted_away_goals - actual_away_goals) <= 1.0 
                        THEN 1 END) as goals_within_1
                FROM predictions
                WHERE actual_outcome IS NOT NULL
                AND prediction_date >= NOW() - INTERVAL '30 days'
            """)
            
            recent_stats = cursor.fetchone()
        
        if recent_stats and recent_stats['total_predictions'] > 0:
            recent_goals_accuracy = recent_stats['goals_within_1'] / recent_stats['total_predictions']
            print(f"   • Total Predictions: {recent_stats['total_predictions']}")
            print(f"   • Outcome Accuracy: {recent_stats['outcome_accuracy']:.3f}")
            print(f"   • Goals Accuracy (±1): {recent_goals_accuracy:.3f}")
        else:
            print("   • No recent completed predictions found")
            
    except Exception as e:
        logger.error(f"Error in prediction accuracy analysis: {e}")
        print(f"❌ Error: {e}")


def single_prediction_menu():
    """Single prediction interface."""
    print("\n" + "=" * 50)
    print("🎲 MAKE SINGLE PREDICTION")
    print("=" * 50)
    
    try:
        predictor = FootballPredictor()
        
        # Check if model is available
        if not predictor.load_model():
            print("\n❌ No trained model available.")
            print("   Please train a model first using 'Model Management' → 'Model Training'.")
            return
        
        # Get upcoming games
        games = predictor.get_upcoming_games_without_predictions()
        
        if not games:
            print("\n❌ No upcoming games available for prediction.")
            return
        
        print(f"\n📋 Select a game to predict:")
        print()
        
        for i, game in enumerate(games[:10], 1):
            print(f"{i}. {game['home_team_name']} vs {game['away_team_name']}")
            print(f"   • League: {game['league_name']}")
            print(f"   • Date: {game['date']}")
            print()
        
        if len(games) > 10:
            print(f"   ... and {len(games) - 10} more games (showing first 10)")
            print()
        
        try:
            choice = int(input("Enter game number: ").strip())
            if choice < 1 or choice > min(len(games), 10):
                print("❌ Invalid game selection.")
                return
            
            selected_game = games[choice - 1]
            
        except ValueError:
            print("❌ Invalid input. Please enter a number.")
            return
        
        # Make prediction
        print(f"\n🔮 Making prediction for:")
        print(f"   {selected_game['home_team_name']} vs {selected_game['away_team_name']}")
        print()
        
        prediction = predictor.predict_match(selected_game)
        
        if prediction:
            outcome_names = {1: "Home Win", 0: "Draw", 2: "Away Win"}
            predicted_outcome = outcome_names.get(prediction['predicted_outcome'].value, 'Unknown')
            
            print("✅ Prediction completed!")
            print()
            print("🔮 Prediction Results:")
            print(f"   • Outcome: {predicted_outcome}")
            print(f"   • Score: {prediction['predicted_home_goals']:.1f} - {prediction['predicted_away_goals']:.1f}")
            print(f"   • Total Goals: {prediction['predicted_total_goals']:.1f}")
            print()
            print("🎯 Confidence Levels:")
            print(f"   • Home Win: {prediction['confidence_home_win']:.3f}")
            print(f"   • Draw: {prediction['confidence_draw']:.3f}")
            print(f"   • Away Win: {prediction['confidence_away_win']:.3f}")
            print(f"   • Over 2.5 Goals: {prediction['confidence_over_2_5']:.3f}")
            print(f"   • Under 2.5 Goals: {prediction['confidence_under_2_5']:.3f}")
            
            # Ask to save prediction
            save = input(f"\n💾 Save this prediction to database? (y/n): ").lower()
            if save == 'y':
                if predictor.save_prediction(prediction):
                    print("✅ Prediction saved successfully!")
                else:
                    print("❌ Failed to save prediction.")
        else:
            print("❌ Failed to make prediction.")
            
    except Exception as e:
        logger.error(f"Error in single prediction menu: {e}")
        print(f"❌ Error: {e}")
