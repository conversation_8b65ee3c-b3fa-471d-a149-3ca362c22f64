#!/usr/bin/env python3
"""
Beautiful predictions viewer with time period options and colorful display.
"""
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from database.connection import get_db_cursor

logger = logging.getLogger(__name__)

# Color codes for terminal output
class Colors:
    """ANSI color codes for terminal output."""
    RESET = '\033[0m'
    BOLD = '\033[1m'
    DIM = '\033[2m'

    # Text colors
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'

    # Background colors
    BG_RED = '\033[41m'
    BG_GREEN = '\033[42m'
    BG_YELLOW = '\033[43m'
    BG_BLUE = '\033[44m'
    BG_MAGENTA = '\033[45m'
    BG_CYAN = '\033[46m'

    # Bright colors
    BRIGHT_RED = '\033[91m'
    BRIGHT_GREEN = '\033[92m'
    BRIGHT_YELLOW = '\033[93m'
    BRIGHT_BLUE = '\033[94m'
    BRIGHT_MAGENTA = '\033[95m'
    BRIGHT_CYAN = '\033[96m'
    BRIGHT_WHITE = '\033[97m'

    # Additional colors for better display
    ORANGE = '\033[38;5;208m'


def colorize(text: str, color: str) -> str:
    """Add color to text."""
    return f"{color}{text}{Colors.RESET}"


def view_recent_predictions_menu():
    """Enhanced predictions viewer with time period options."""
    while True:
        print("\n" + "=" * 70)
        print(f"{Colors.BOLD}{Colors.BRIGHT_CYAN}📊 PREDICTIONS OVERZICHT{Colors.RESET}")
        print("=" * 70)
        print()

        # Time period options with emojis and colors
        print(f"{Colors.BOLD}Kies een tijdsperiode:{Colors.RESET}")
        print()
        print(f"  {Colors.BRIGHT_GREEN}1.{Colors.RESET} 📅 {Colors.GREEN}Vandaag{Colors.RESET}")
        print(f"  {Colors.BRIGHT_BLUE}2.{Colors.RESET} 🌅 {Colors.BLUE}Gisteren{Colors.RESET}")
        print(f"  {Colors.BRIGHT_YELLOW}3.{Colors.RESET} 🌄 {Colors.YELLOW}Morgen{Colors.RESET}")
        print(f"  {Colors.BRIGHT_MAGENTA}4.{Colors.RESET} 📆 {Colors.MAGENTA}Laatste 7 dagen{Colors.RESET}")
        print(f"  {Colors.BRIGHT_CYAN}5.{Colors.RESET} 📊 {Colors.CYAN}Laatste 30 dagen{Colors.RESET}")
        print(f"  {Colors.BRIGHT_RED}6.{Colors.RESET} 🔄 {Colors.RED}Alle recente predictions{Colors.RESET}")
        print()
        print(f"  {Colors.DIM}0. ⬅️  Terug naar hoofdmenu{Colors.RESET}")
        print()

        choice = input(f"{Colors.BOLD}Voer je keuze in: {Colors.RESET}").strip()

        if choice == '1':
            show_predictions_for_period("today", "📅 Predictions van Vandaag")
        elif choice == '2':
            show_predictions_for_period("yesterday", "🌅 Predictions van Gisteren")
        elif choice == '3':
            show_predictions_for_period("tomorrow", "🌄 Predictions voor Morgen")
        elif choice == '4':
            show_predictions_for_period("last_7_days", "📆 Predictions van Laatste 7 Dagen")
        elif choice == '5':
            show_predictions_for_period("last_30_days", "📊 Predictions van Laatste 30 Dagen")
        elif choice == '6':
            show_predictions_for_period("all_recent", "🔄 Alle Recente Predictions")
        elif choice == '0':
            return
        else:
            print(f"{Colors.BRIGHT_RED}❌ Ongeldige keuze. Probeer opnieuw.{Colors.RESET}")

        input(f"\n{Colors.DIM}Druk op Enter om door te gaan...{Colors.RESET}")


def show_predictions_for_period(period: str, title: str):
    """Show predictions for a specific time period."""
    try:
        # Get date range based on period
        date_filter, params = get_date_filter_for_period(period)

        # Query predictions
        predictions = get_predictions_for_period(date_filter, params)

        if not predictions:
            print(f"\n{Colors.YELLOW}📭 Geen predictions gevonden voor deze periode.{Colors.RESET}")
            print(f"{Colors.DIM}   Maak eerst predictions met 'Predict Upcoming Games'.{Colors.RESET}")
            return

        # Display header
        print(f"\n{Colors.BOLD}{Colors.BRIGHT_CYAN}{title}{Colors.RESET}")
        print("=" * len(title.replace('📅', '').replace('🌅', '').replace('🌄', '').replace('📆', '').replace('📊', '').replace('🔄', '').strip()))
        print()

        # Group predictions by date
        grouped_predictions = group_predictions_by_date(predictions)

        # Display predictions
        total_predictions = 0
        correct_predictions = 0

        for date_str, date_predictions in grouped_predictions.items():
            display_predictions_for_date(date_str, date_predictions)

            # Count statistics
            total_predictions += len(date_predictions)
            for pred in date_predictions:
                if pred['actual_outcome'] is not None and pred['predicted_outcome'] == pred['actual_outcome']:
                    correct_predictions += 1

        # Display summary statistics
        display_summary_statistics(total_predictions, correct_predictions, period)

    except Exception as e:
        logger.error(f"Error showing predictions for period {period}: {e}")
        print(f"{Colors.BRIGHT_RED}❌ Fout bij het ophalen van predictions: {e}{Colors.RESET}")


def get_date_filter_for_period(period: str) -> tuple:
    """Get SQL date filter and parameters for the specified period."""
    today = datetime.now().date()

    if period == "today":
        return "DATE(COALESCE(ug.date, m.date)) = %s", (today,)
    elif period == "yesterday":
        yesterday = today - timedelta(days=1)
        return "DATE(COALESCE(ug.date, m.date)) = %s", (yesterday,)
    elif period == "tomorrow":
        tomorrow = today + timedelta(days=1)
        return "DATE(COALESCE(ug.date, m.date)) = %s", (tomorrow,)
    elif period == "last_7_days":
        week_ago = today - timedelta(days=7)
        return "DATE(COALESCE(ug.date, m.date)) >= %s AND DATE(COALESCE(ug.date, m.date)) <= %s", (week_ago, today)
    elif period == "last_30_days":
        month_ago = today - timedelta(days=30)
        return "DATE(COALESCE(ug.date, m.date)) >= %s AND DATE(COALESCE(ug.date, m.date)) <= %s", (month_ago, today)
    else:  # all_recent
        month_ago = today - timedelta(days=60)
        return "DATE(COALESCE(ug.date, m.date)) >= %s", (month_ago,)


def get_predictions_for_period(date_filter: str, params: tuple) -> List[Dict[str, Any]]:
    """Get predictions from database for the specified period."""
    try:
        with get_db_cursor() as cursor:
            query = f"""
                SELECT
                    p.match_id,
                    p.predicted_outcome,
                    p.predicted_home_goals,
                    p.predicted_away_goals,
                    p.predicted_total_goals,
                    p.confidence_home_win,
                    p.confidence_draw,
                    p.confidence_away_win,
                    p.confidence_over_2_5,
                    p.confidence_under_2_5,
                    p.prediction_date,
                    p.model_version,
                    COALESCE(ug.date, m.date) as match_date,
                    COALESCE(ht_ug.name, ht_m.name) as home_team_name,
                    COALESCE(at_ug.name, at_m.name) as away_team_name,
                    COALESCE(l_ug.name, l_m.name) as league_name,
                    p.actual_outcome,
                    p.actual_home_goals,
                    p.actual_away_goals,
                    COALESCE(ug.status, m.status) as match_status
                FROM predictions p
                LEFT JOIN upcoming_games ug ON p.match_id = ug.id
                LEFT JOIN matches m ON p.match_id = m.id
                LEFT JOIN teams ht_ug ON ug.home_team_id = ht_ug.id
                LEFT JOIN teams at_ug ON ug.away_team_id = at_ug.id
                LEFT JOIN teams ht_m ON m.home_team_id = ht_m.id
                LEFT JOIN teams at_m ON m.away_team_id = at_m.id
                LEFT JOIN leagues l_ug ON ug.league_id = l_ug.id AND ug.season = l_ug.season
                LEFT JOIN leagues l_m ON m.league_id = l_m.id AND m.season = l_m.season
                WHERE {date_filter}
                ORDER BY COALESCE(ug.date, m.date) DESC, p.prediction_date DESC
            """

            cursor.execute(query, params)
            return cursor.fetchall()

    except Exception as e:
        logger.error(f"Error getting predictions for period: {e}")
        return []


def group_predictions_by_date(predictions: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """Group predictions by match date."""
    grouped = {}

    for pred in predictions:
        match_date = pred['match_date']
        if match_date:
            date_str = match_date.strftime('%Y-%m-%d')
            if date_str not in grouped:
                grouped[date_str] = []
            grouped[date_str].append(pred)

    return grouped


def display_predictions_for_date(date_str: str, predictions: List[Dict[str, Any]]):
    """Display predictions for a specific date with beautiful formatting."""
    # Parse date for nice formatting
    try:
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        today = datetime.now().date()

        # Determine date label
        if date_obj.date() == today:
            date_label = f"📅 {Colors.BRIGHT_GREEN}Vandaag{Colors.RESET}"
        elif date_obj.date() == today - timedelta(days=1):
            date_label = f"🌅 {Colors.BRIGHT_BLUE}Gisteren{Colors.RESET}"
        elif date_obj.date() == today + timedelta(days=1):
            date_label = f"🌄 {Colors.BRIGHT_YELLOW}Morgen{Colors.RESET}"
        else:
            weekday = date_obj.strftime('%A')
            weekday_dutch = {
                'Monday': 'Maandag', 'Tuesday': 'Dinsdag', 'Wednesday': 'Woensdag',
                'Thursday': 'Donderdag', 'Friday': 'Vrijdag', 'Saturday': 'Zaterdag', 'Sunday': 'Zondag'
            }
            weekday_nl = weekday_dutch.get(weekday, weekday)
            date_label = f"📆 {Colors.CYAN}{weekday_nl} {date_obj.strftime('%d-%m-%Y')}{Colors.RESET}"

        print(f"\n{Colors.BOLD}{date_label}{Colors.RESET}")
        print(f"{Colors.DIM}{'─' * 60}{Colors.RESET}")

    except:
        print(f"\n{Colors.BOLD}📆 {date_str}{Colors.RESET}")
        print(f"{Colors.DIM}{'─' * 60}{Colors.RESET}")

    # Display each prediction
    for i, pred in enumerate(predictions, 1):
        display_single_prediction(pred, i)


def display_single_prediction(pred: Dict[str, Any], index: int):
    """Display a single prediction with beautiful formatting and colors."""
    outcome_names = {1: "Home Win", 0: "Draw", 2: "Away Win"}
    outcome_emojis = {1: "🏠", 0: "🤝", 2: "✈️"}
    outcome_colors = {1: Colors.GREEN, 0: Colors.YELLOW, 2: Colors.BLUE}

    # Match info
    home_team = pred['home_team_name'] or 'Unknown'
    away_team = pred['away_team_name'] or 'Unknown'
    league = pred['league_name'] or 'Unknown League'

    print(f"\n{Colors.BOLD}{index}.{Colors.RESET} {Colors.BRIGHT_WHITE}{home_team}{Colors.RESET} vs {Colors.BRIGHT_WHITE}{away_team}{Colors.RESET}")
    print(f"   {Colors.DIM}🏆 {league}{Colors.RESET}")

    # Prediction details
    predicted_outcome = pred['predicted_outcome']
    predicted_outcome_name = outcome_names.get(predicted_outcome, 'Unknown')
    predicted_emoji = outcome_emojis.get(predicted_outcome, '❓')
    predicted_color = outcome_colors.get(predicted_outcome, Colors.WHITE)

    print(f"   {Colors.BOLD}🔮 Prediction:{Colors.RESET} {predicted_emoji} {colorize(predicted_outcome_name, predicted_color)}")

    # Score prediction
    home_goals = pred['predicted_home_goals']
    away_goals = pred['predicted_away_goals']
    total_goals = pred['predicted_total_goals']

    print(f"   {Colors.BOLD}⚽ Score:{Colors.RESET} {colorize(f'{home_goals:.1f}', Colors.GREEN)} - {colorize(f'{away_goals:.1f}', Colors.BLUE)} {Colors.DIM}(totaal: {total_goals:.1f}){Colors.RESET}")

    # Confidence levels with color coding
    conf_home = pred['confidence_home_win']
    conf_draw = pred['confidence_draw']
    conf_away = pred['confidence_away_win']

    # Color confidence based on level
    def get_confidence_color(conf):
        if conf >= 0.7:
            return Colors.BRIGHT_GREEN
        elif conf >= 0.5:
            return Colors.YELLOW
        elif conf >= 0.3:
            return Colors.ORANGE
        else:
            return Colors.RED

    print(f"   {Colors.BOLD}📊 Confidence:{Colors.RESET} ", end="")
    print(f"H:{colorize(f'{conf_home:.2f}', get_confidence_color(conf_home))} ", end="")
    print(f"D:{colorize(f'{conf_draw:.2f}', get_confidence_color(conf_draw))} ", end="")
    print(f"A:{colorize(f'{conf_away:.2f}', get_confidence_color(conf_away))}")

    # Over/Under 2.5 goals
    if pred['confidence_over_2_5'] is not None:
        over_2_5 = pred['confidence_over_2_5']
        under_2_5 = pred['confidence_under_2_5']
        print(f"   {Colors.BOLD}🎯 Goals:{Colors.RESET} Over 2.5: {colorize(f'{over_2_5:.2f}', get_confidence_color(over_2_5))} | Under 2.5: {colorize(f'{under_2_5:.2f}', get_confidence_color(under_2_5))}")

    # Actual result if available
    actual_outcome = pred['actual_outcome']
    if actual_outcome is not None:
        actual_outcome_name = outcome_names.get(actual_outcome, 'Unknown')
        actual_emoji = outcome_emojis.get(actual_outcome, '❓')
        actual_color = outcome_colors.get(actual_outcome, Colors.WHITE)

        # Check if prediction was correct
        is_correct = predicted_outcome == actual_outcome
        status_emoji = "✅" if is_correct else "❌"
        status_color = Colors.BRIGHT_GREEN if is_correct else Colors.BRIGHT_RED
        status_text = "Correct" if is_correct else "Incorrect"

        print(f"   {Colors.BOLD}🏆 Resultaat:{Colors.RESET} {actual_emoji} {colorize(actual_outcome_name, actual_color)} {colorize(f'({status_emoji} {status_text})', status_color)}")

        # Actual score if available
        if pred['actual_home_goals'] is not None and pred['actual_away_goals'] is not None:
            actual_home = pred['actual_home_goals']
            actual_away = pred['actual_away_goals']
            print(f"   {Colors.BOLD}⚽ Eindstand:{Colors.RESET} {colorize(str(actual_home), Colors.GREEN)} - {colorize(str(actual_away), Colors.BLUE)}")

            # Goal prediction accuracy
            home_diff = abs(home_goals - actual_home)
            away_diff = abs(away_goals - actual_away)
            goals_accurate = home_diff <= 1.0 and away_diff <= 1.0
            goals_status = "✅ Goed" if goals_accurate else "❌ Niet goed"
            goals_color = Colors.BRIGHT_GREEN if goals_accurate else Colors.BRIGHT_RED
            print(f"   {Colors.BOLD}🎯 Goals accuracy:{Colors.RESET} {colorize(goals_status, goals_color)} {Colors.DIM}(verschil: {home_diff:.1f}, {away_diff:.1f}){Colors.RESET}")
    else:
        # Match not completed yet
        match_status = pred['match_status']
        if match_status in ['NS', 'TBD']:
            print(f"   {colorize('⏳ Wedstrijd nog niet gespeeld', Colors.YELLOW)}")
        elif match_status in ['LIVE', '1H', '2H', 'HT']:
            print(f"   {colorize('🔴 Wedstrijd bezig', Colors.BRIGHT_RED)}")
        else:
            print(f"   {colorize('⏳ Resultaat nog niet beschikbaar', Colors.YELLOW)}")

    # Model info
    model_version = pred['model_version']
    prediction_date = pred['prediction_date']
    if prediction_date:
        pred_date_str = prediction_date.strftime('%d-%m-%Y %H:%M')
        print(f"   {Colors.DIM}🤖 Model: {model_version} | Voorspeld op: {pred_date_str}{Colors.RESET}")


def display_summary_statistics(total_predictions: int, correct_predictions: int, period: str):
    """Display summary statistics for the predictions."""
    print(f"\n{Colors.BOLD}{Colors.BRIGHT_CYAN}📈 SAMENVATTING{Colors.RESET}")
    print(f"{Colors.DIM}{'─' * 40}{Colors.RESET}")

    print(f"{Colors.BOLD}📊 Totaal predictions:{Colors.RESET} {colorize(str(total_predictions), Colors.BRIGHT_WHITE)}")

    if total_predictions > 0:
        accuracy = correct_predictions / total_predictions
        accuracy_percentage = accuracy * 100

        # Color code accuracy
        if accuracy >= 0.7:
            accuracy_color = Colors.BRIGHT_GREEN
            accuracy_emoji = "🎯"
        elif accuracy >= 0.6:
            accuracy_color = Colors.GREEN
            accuracy_emoji = "✅"
        elif accuracy >= 0.5:
            accuracy_color = Colors.YELLOW
            accuracy_emoji = "🟡"
        else:
            accuracy_color = Colors.RED
            accuracy_emoji = "❌"

        print(f"{Colors.BOLD}✅ Correcte predictions:{Colors.RESET} {colorize(str(correct_predictions), Colors.BRIGHT_GREEN)}")
        print(f"{Colors.BOLD}{accuracy_emoji} Nauwkeurigheid:{Colors.RESET} {colorize(f'{accuracy_percentage:.1f}%', accuracy_color)}")

        # Performance rating
        if accuracy >= 0.7:
            rating = f"{Colors.BRIGHT_GREEN}🏆 Uitstekend{Colors.RESET}"
        elif accuracy >= 0.6:
            rating = f"{Colors.GREEN}⭐ Zeer goed{Colors.RESET}"
        elif accuracy >= 0.5:
            rating = f"{Colors.YELLOW}👍 Goed{Colors.RESET}"
        else:
            rating = f"{Colors.RED}📈 Kan beter{Colors.RESET}"

        print(f"{Colors.BOLD}🏅 Beoordeling:{Colors.RESET} {rating}")
    else:
        print(f"{Colors.YELLOW}📭 Geen afgeronde wedstrijden om nauwkeurigheid te berekenen{Colors.RESET}")

    print()
