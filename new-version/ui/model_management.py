"""
Model management UI for training and managing prediction models.
"""
import logging
from ml import ModelManager

logger = logging.getLogger(__name__)


def model_management_menu():
    """Model management submenu."""
    print("\n" + "=" * 60)
    print("🤖 MODEL MANAGEMENT")
    print("=" * 60)
    print()
    print("Available options:")
    print("  1. 🎯 Model Training")
    print("  2. 📊 View Model Information")
    print("  3. 🧠 Adaptive Learning")
    print("  4. 📈 List All Models")
    print("  0. Back to Main Menu")
    print()

    choice = input("Enter your choice: ").strip()

    if choice == '1':
        model_training_menu()
    elif choice == '2':
        view_model_info_menu()
    elif choice == '3':
        adaptive_learning_menu()
    elif choice == '4':
        list_models_menu()
    elif choice == '0':
        return
    else:
        print("❌ Invalid choice.")

    input("\nPress Enter to continue...")


def model_training_menu():
    """Model training interface."""
    print("\n" + "=" * 50)
    print("🎯 MODEL TRAINING")
    print("=" * 50)
    
    try:
        model_manager = ModelManager()
        
        # Check if model already exists
        existing_model = model_manager.check_existing_model()
        
        if existing_model:
            print("\n📋 Existing model detected!")
            print("   An active model already exists in the database.")
            print("   Training will create a new version and replace the current model.")
            print()
            
            # Show current model info
            model_info = model_manager.get_model_info()
            if model_info:
                print("📊 Current Model Information:")
                print(f"   • Name: {model_info['name']}")
                print(f"   • Version: {model_info['version']}")
                print(f"   • Type: {model_info['model_type']}")
                print(f"   • Training Date: {model_info['training_date']}")
                print(f"   • Training Samples: {model_info['training_samples']}")
                print(f"   • Validation Accuracy: {model_info['validation_accuracy']:.3f}")
                print()
        else:
            print("\n📋 No existing model found.")
            print("   A new model will be trained from scratch.")
            print()
        
        # Get training data info
        training_data = model_manager.get_training_data()
        if training_data is None:
            print("❌ Insufficient training data available.")
            print("   Please ensure you have at least 50 completed matches with results.")
            print("   Use 'Data Management' → 'Fetch Football Data' to get more data.")
            return
        
        print(f"✅ Training data available: {len(training_data)} matches")
        print()
        
        # Choose model type
        print("🤖 Select model type:")
        print("  1. XGBoost (Recommended)")
        print("  2. LightGBM")
        print("  3. Random Forest")
        print()
        
        model_choice = input("Enter your choice (1-3): ").strip()
        
        model_types = {
            '1': 'xgboost',
            '2': 'lightgbm', 
            '3': 'random_forest'
        }
        
        if model_choice not in model_types:
            print("❌ Invalid model type selection.")
            return
        
        model_type = model_types[model_choice]
        print(f"\n✅ Selected model type: {model_type}")
        
        # Confirm training
        action = "Retrain existing model" if existing_model else "Train new model"
        confirm = input(f"\n✓ {action} with {model_type}? (y/n): ").lower()
        if confirm != 'y':
            print("❌ Training cancelled.")
            return
        
        # Start training
        print(f"\n🚀 Starting model training...")
        print("   This may take a few minutes depending on the amount of data...")
        print()
        
        result = model_manager.train_model(model_type=model_type)
        
        if result['success']:
            print("✅ Model training completed successfully!")
            print()
            print("📊 Training Results:")
            print(f"   • Model Name: {result['model_name']}")
            print(f"   • Version: {result['version']}")
            print(f"   • Model Type: {result['model_type']}")
            print(f"   • Training Samples: {result['training_samples']}")
            print()
            print("🎯 Model Accuracies:")
            accuracies = result['accuracies']
            print(f"   • Outcome Accuracy: {accuracies['outcome']:.3f}")
            print(f"   • Home Goals MAE: {accuracies['home_goals']:.3f}")
            print(f"   • Away Goals MAE: {accuracies['away_goals']:.3f}")
            print()
            
            if result['saved_to_db']:
                print("💾 Model saved to database and activated!")
                print("   You can now use this model for predictions.")
            else:
                print("⚠️  Model trained but failed to save to database.")
        else:
            print(f"❌ Model training failed: {result['error']}")
            
    except Exception as e:
        logger.error(f"Error in model training menu: {e}")
        print(f"❌ Error: {e}")


def view_model_info_menu():
    """View model information interface."""
    print("\n" + "=" * 50)
    print("📊 MODEL INFORMATION")
    print("=" * 50)
    
    try:
        model_manager = ModelManager()
        model_info = model_manager.get_model_info()
        
        if not model_info:
            print("\n❌ No active model found.")
            print("   Train a model first using 'Model Training'.")
            return
        
        print("\n📊 Active Model Information:")
        print(f"   • Name: {model_info['name']}")
        print(f"   • Version: {model_info['version']}")
        print(f"   • Type: {model_info['model_type']}")
        print(f"   • Training Date: {model_info['training_date']}")
        print(f"   • Training Samples: {model_info['training_samples']}")
        print(f"   • Validation Accuracy: {model_info['validation_accuracy']:.3f}")
        print(f"   • Status: {'Active' if model_info['is_active'] else 'Inactive'}")
        print()
        
        # Test model loading
        print("🔄 Testing model loading...")
        if model_manager.load_model():
            print("✅ Model loaded successfully!")
            print(f"   • Features: {len(model_manager.feature_columns)} columns")
            print(f"   • Models: {list(model_manager.models.keys()) if model_manager.models else 'None'}")
        else:
            print("❌ Failed to load model.")
            
    except Exception as e:
        logger.error(f"Error viewing model info: {e}")
        print(f"❌ Error: {e}")


def adaptive_learning_menu():
    """Adaptive learning interface."""
    print("\n" + "=" * 50)
    print("🧠 ADAPTIVE LEARNING")
    print("=" * 50)
    
    try:
        from ml import AdaptiveLearningSystem, run_adaptive_learning
        
        print("\n📋 Adaptive Learning Options:")
        print("  1. 🔄 Run Continuous Learning Cycle")
        print("  2. 📊 Analyze Current Performance")
        print("  3. 🎯 Force Adaptive Retraining")
        print("  0. Back to Model Management")
        print()
        
        choice = input("Enter your choice: ").strip()
        
        if choice == '1':
            print("\n🔄 Running continuous learning cycle...")
            print("   This will analyze model performance and retrain if needed.")
            print()
            
            result = run_adaptive_learning()
            
            if result['success']:
                if result.get('learned', False):
                    print("✅ Adaptive learning completed: Model improved!")
                    retrain_results = result.get('retrain_results', {})
                    print(f"   • Samples used: {retrain_results.get('samples_used', 'N/A')}")
                    print(f"   • New accuracy: {retrain_results.get('new_accuracy', 'N/A'):.3f}")
                    print(f"   • Reasons: {', '.join(result.get('reasons', []))}")
                else:
                    print("✅ Adaptive learning completed: No improvement needed")
                    print(f"   • Message: {result.get('message', 'Model performance is satisfactory')}")
            else:
                print(f"❌ Adaptive learning failed: {result['error']}")
        
        elif choice == '2':
            print("\n📊 Analyzing current performance...")
            
            learning_system = AdaptiveLearningSystem()
            if not learning_system.load_model():
                print("❌ No model available for analysis.")
                return
            
            analysis = learning_system.analyze_prediction_performance(days_back=30)
            
            if analysis:
                print("✅ Performance analysis completed!")
                print()
                print("📈 Performance Metrics (Last 30 days):")
                print(f"   • Total Predictions: {analysis['total_predictions']}")
                print(f"   • Outcome Accuracy: {analysis['outcome_accuracy']:.3f}")
                print(f"   • Goals Accuracy: {analysis['goals_accuracy']:.3f}")
                print(f"   • High Confidence Predictions: {analysis['high_confidence_count']}")
                print(f"   • High Confidence Accuracy: {analysis['high_confidence_accuracy']:.3f}")
                print(f"   • Confidence Calibration: {analysis['confidence_calibration']:.3f}")
            else:
                print("❌ Insufficient data for performance analysis.")
        
        elif choice == '3':
            print("\n🎯 Force adaptive retraining...")
            print("   This will retrain the model regardless of current performance.")
            
            confirm = input("\n✓ Continue with forced retraining? (y/n): ").lower()
            if confirm != 'y':
                print("❌ Retraining cancelled.")
                return
            
            learning_system = AdaptiveLearningSystem()
            if not learning_system.load_model():
                print("❌ No model available for retraining.")
                return
            
            # Force retraining by lowering thresholds temporarily
            learning_system.min_accuracy_threshold = 0.0
            result = learning_system.adaptive_retraining()
            
            if result['success'] and result.get('retrained', False):
                print("✅ Forced retraining completed!")
                retrain_results = result.get('retrain_results', {})
                print(f"   • Samples used: {retrain_results.get('samples_used', 'N/A')}")
                print(f"   • New accuracy: {retrain_results.get('new_accuracy', 'N/A'):.3f}")
            else:
                print(f"❌ Forced retraining failed: {result.get('error', 'Unknown error')}")
        
        elif choice == '0':
            return
        else:
            print("❌ Invalid choice.")
            
    except Exception as e:
        logger.error(f"Error in adaptive learning menu: {e}")
        print(f"❌ Error: {e}")


def list_models_menu():
    """List all models interface."""
    print("\n" + "=" * 50)
    print("📈 ALL MODELS")
    print("=" * 50)
    
    try:
        model_manager = ModelManager()
        models = model_manager.list_models()
        
        if not models:
            print("\n❌ No models found in database.")
            print("   Train a model first using 'Model Training'.")
            return
        
        print(f"\n📊 Found {len(models)} models:")
        print()
        
        for i, model in enumerate(models, 1):
            status = "🟢 Active" if model['is_active'] else "⚪ Inactive"
            print(f"{i}. {model['name']} v{model['version']} ({status})")
            print(f"   • Type: {model['model_type']}")
            print(f"   • Training Date: {model['training_date']}")
            print(f"   • Training Samples: {model['training_samples']}")
            print(f"   • Validation Accuracy: {model['validation_accuracy']:.3f}")
            print()
            
    except Exception as e:
        logger.error(f"Error listing models: {e}")
        print(f"❌ Error: {e}")
