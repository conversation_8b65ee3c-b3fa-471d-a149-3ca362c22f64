"""
Main menu interface for the Football Prediction System.
"""
import logging
from typing import Dict, Callable

from config import config
from database.connection import test_connection
from api import FootballApiClient

# Import UI functions
from .fetch_football_data import fetch_football_data_menu
from .fetch_upcoming_games import fetch_upcoming_games_menu
from .update_results import update_match_results_menu
from .database_stats import view_database_stats_menu
from .debug_missing_stats import debug_missing_stats_menu, analyze_statistics_coverage
from .model_management import model_management_menu
from .prediction_management import prediction_management_menu

logger = logging.getLogger(__name__)


class MainMenu:
    """Main menu interface."""

    def __init__(self):
        """Initialize main menu."""
        self.running = True
        self.menu_options: Dict[str, Callable] = {
            '1': self.data_management_menu,
            '2': self.model_management_menu,
            '3': self.prediction_menu,
            '4': self.system_status_menu,
            '5': self.settings_menu,
            '0': self.exit_application
        }

    def run(self):
        """Run the main menu loop."""
        while self.running:
            self.display_main_menu()
            choice = input("\nEnter your choice: ").strip()

            if choice in self.menu_options:
                try:
                    self.menu_options[choice]()
                except Exception as e:
                    logger.error(f"Error in menu option {choice}: {e}")
                    print(f"❌ Error: {e}")
                    input("\nPress Enter to continue...")
            else:
                print("❌ Invalid choice. Please try again.")
                input("\nPress Enter to continue...")

    def display_main_menu(self):
        """Display the main menu."""
        print("\n" + "=" * 60)
        print("🚀 FOOTBALL PREDICTION SYSTEM - NEW VERSION")
        print("=" * 60)
        print()
        print("📋 Main Menu:")
        print("  1. 📊 Data Management")
        print("  2. 🤖 Model Management")
        print("  3. 🎯 Predictions")
        print("  4. 📈 System Status")
        print("  5. ⚙️  Settings")
        print("  0. 🚪 Exit")
        print()

    def data_management_menu(self):
        """Data management submenu."""
        print("\n" + "=" * 50)
        print("📊 DATA MANAGEMENT")
        print("=" * 50)
        print()
        print("Available options:")
        print("  1. 🚀 Fetch Football Data (All-in-One)")
        print("  2. 📅 Fetch Upcoming Games")
        print("  3. 🔄 Update Match Results")
        print("  4. 📊 View Database Statistics")
        print("  5. 🔍 Debug Missing Statistics")
        print("  6. 📈 Analyze Statistics Coverage")
        print("  0. Back to Main Menu")
        print()

        choice = input("Enter your choice: ").strip()

        if choice == '1':
            fetch_football_data_menu()
        elif choice == '2':
            fetch_upcoming_games_menu()
        elif choice == '3':
            update_match_results_menu()
        elif choice == '4':
            view_database_stats_menu()
        elif choice == '5':
            debug_missing_stats_menu()
        elif choice == '6':
            analyze_statistics_coverage()
        elif choice == '0':
            return
        else:
            print("❌ Invalid choice.")

        input("\nPress Enter to continue...")

    def model_management_menu(self):
        """Model management submenu."""
        model_management_menu()

    def prediction_menu(self):
        """Prediction submenu."""
        prediction_management_menu()

    def system_status_menu(self):
        """System status submenu."""
        print("\n" + "=" * 50)
        print("📈 SYSTEM STATUS")
        print("=" * 50)
        print()

        # Database status
        print("🗄️  Database Status:")
        if test_connection():
            print("   ✅ Connected")
        else:
            print("   ❌ Connection failed")

        # API status
        print("\n🌐 API Status:")
        if config.api.football_api_key:
            try:
                api_client = FootballApiClient()
                if api_client.test_connection():
                    print("   ✅ Connected")
                else:
                    print("   ❌ Connection failed")
            except Exception as e:
                print(f"   ❌ Error: {e}")
        else:
            print("   ⚠️  No API key configured")

        # Configuration
        print("\n⚙️  Configuration:")
        print(f"   • Debug mode: {config.debug_mode}")
        print(f"   • Cache enabled: {config.cache_enabled}")
        print(f"   • Default model: {config.model.default_model_type}")
        print(f"   • Log level: {config.logging.level}")

        input("\nPress Enter to continue...")

    def settings_menu(self):
        """Settings submenu."""
        print("\n" + "=" * 50)
        print("⚙️  SETTINGS")
        print("=" * 50)
        print()
        print("🚧 Coming soon! This feature is under development.")
        print()
        print("Planned features:")
        print("  • API configuration")
        print("  • Database settings")
        print("  • Model parameters")
        print("  • Logging configuration")

        input("\nPress Enter to continue...")







    def exit_application(self):
        """Exit the application."""
        print("\n👋 Thank you for using Football Prediction System!")
        print("   Goodbye!")
        self.running = False
