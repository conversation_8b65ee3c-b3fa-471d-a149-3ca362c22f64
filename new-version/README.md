# Football Prediction System - New Version

## 🚀 **Complete Rewrite - Clean & Modern Architecture**

This is a complete rewrite of the football prediction system with:
- **Clean Architecture** - Well-organized, modular code
- **Proper Database Design** - Correct table relationships
- **Adaptive Learning** - Self-improving AI system
- **Modern Python** - Type hints, proper error handling
- **Comprehensive Testing** - Unit tests and integration tests

## 📁 **Project Structure**

```
new-version/
├── README.md                 # This file
├── requirements.txt          # Python dependencies
├── config.py                # Configuration settings
├── main.py                  # Main application entry point
├── database/                # Database layer
│   ├── __init__.py
│   ├── connection.py        # Database connection
│   ├── models.py           # Database models/schemas
│   └── migrations.py       # Database setup/migrations
├── api/                     # External API integration
│   ├── __init__.py
│   ├── client.py           # API client
│   └── football_api.py     # Football-specific API calls
├── core/                    # Core business logic
│   ├── __init__.py
│   ├── data_manager.py     # Data fetching and management
│   ├── model_trainer.py    # ML model training
│   ├── predictor.py        # Prediction engine
│   └── adaptive_learning.py # Self-learning system
├── storage/                 # Data storage layer
│   ├── __init__.py
│   ├── match_storage.py    # Match data storage
│   ├── prediction_storage.py # Prediction storage
│   └── model_storage.py    # Model persistence
├── ui/                      # User interface
│   ├── __init__.py
│   ├── main_menu.py        # Main menu system
│   ├── data_menu.py        # Data management menu
│   ├── model_menu.py       # Model management menu
│   └── prediction_menu.py  # Prediction menu
├── utils/                   # Utility functions
│   ├── __init__.py
│   ├── logger.py           # Logging system
│   ├── validators.py       # Data validation
│   └── helpers.py          # Helper functions
└── tests/                   # Test suite
    ├── __init__.py
    ├── test_database.py
    ├── test_models.py
    ├── test_predictions.py
    └── test_adaptive_learning.py
```

## 🎯 **Key Features**

### **1. Clean Database Design**
- **`matches`** - All completed matches
- **`upcoming_games`** - Matches to be played
- **`predictions`** - All predictions with results
- **`teams`** - Team information
- **`leagues`** - League information

### **2. Adaptive Learning System**
- **Self-Learning** - Model improves from its own predictions
- **Performance Monitoring** - Tracks accuracy over time
- **Automatic Retraining** - Triggers when performance drops
- **Confidence Scoring** - Provides prediction confidence

### **3. Modern Architecture**
- **Type Hints** - Full type annotation
- **Error Handling** - Comprehensive error management
- **Logging** - Detailed logging system
- **Configuration** - Centralized config management

## 🚀 **Quick Start**

1. **Install Dependencies**
   ```bash
   cd new-version
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Configure Settings**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and database settings
   # API-Sports API: https://v3.football.api-sports.io
   ```

3. **Initialize Database**
   ```bash
   python -c "from database.migrations import setup_database; setup_database()"
   ```

4. **Run Application**
   ```bash
   python main.py
   ```

## 📊 **Database Schema**

### **Matches Table**
- Stores all completed matches
- Includes scores, statistics, and metadata

### **Upcoming Games Table**
- Stores matches to be played
- Automatically moved to matches when completed

### **Predictions Table**
- Stores all predictions with confidence scores
- Links to matches regardless of completion status
- Tracks accuracy and learning data

### **Teams & Leagues Tables**
- Master data for teams and leagues
- Supports multiple seasons and competitions

## 🧠 **Adaptive Learning**

The system continuously learns from its predictions:

1. **Performance Monitoring** - Tracks prediction accuracy
2. **Data Collection** - Gathers results from completed matches
3. **Model Evaluation** - Analyzes performance trends
4. **Automatic Retraining** - Improves model when needed
5. **Confidence Adjustment** - Updates confidence scoring

## 🔧 **Configuration**

All settings are centralized in `.env` file:
- Database connection settings
- API keys and endpoints (API-Sports: https://v3.football.api-sports.io)
- Model parameters
- Learning thresholds
- Logging configuration

### **API Configuration**
This system uses the **API-Sports** API (not RapidAPI):
- **Base URL**: `https://v3.football.api-sports.io`
- **Header**: `x-apisports-key: YOUR_API_KEY`
- **Documentation**: https://www.api-football.com/documentation-v3

## 🧪 **Testing**

Run the test suite:
```bash
python -m pytest tests/
```

## 📝 **Development**

This version follows modern Python best practices:
- Type hints for all functions
- Comprehensive error handling
- Modular, testable code
- Clear separation of concerns
- Detailed documentation

## 🎮 **Usage**

The system provides an intuitive menu-driven interface:
1. **Data Management** - Fetch and manage match data
2. **Model Training** - Train and evaluate models
3. **Predictions** - Make and track predictions
4. **Adaptive Learning** - Monitor and improve performance

---

**Built with ❤️ for accurate football predictions**
