# Football Prediction System - Environment Configuration
# Copy this file to .env and update with your settings

# Database Configuration
DB_HOST=*************
DB_PORT=5432
DB_NAME=football
DB_USER=postgres
DB_PASSWORD=postgres

# Football API Configuration (API-Sports Direct)
FOOTBALL_API_KEY=your_api_key_here
FOOTBALL_API_HOST=v3.football.api-sports.io
FOOTBALL_API_URL=https://v3.football.api-sports.io

# API Rate Limiting (balanced for stability)
API_RATE_LIMIT=0.5
API_TIMEOUT=20

# Model Configuration
DEFAULT_MODEL_TYPE=random_forest
MODEL_TEST_SIZE=0.2
M<PERSON>EL_RANDOM_STATE=42

# Adaptive Learning Configuration
MIN_PREDICTIONS_LEARNING=10
PERFORMANCE_THRESHOLD=0.6
RETRAINING_THRESHOLD=0.05
LEARNING_CYCLE_INTERVAL=24
MAX_LEARNING_DATA_AGE=30
CONFIDENCE_THRESHOLD=0.7

# Application Settings
CACHE_ENABLED=true
CACHE_TTL=3600
DEBUG_MODE=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/football_predictions.log
