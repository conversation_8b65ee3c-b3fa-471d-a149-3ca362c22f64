# 🎯 Predictions Viewer - Gebruikershandleiding

## 📋 **Overzicht**

De nieuwe Predictions Viewer is een mooie, gebruiksvriendelijke interface voor het bekijken van football predictions met kleurrijke weergave en verschillende tijdsperiode opties.

---

## 🎨 **Features**

### **✅ Kleurrijke Interface**
- **ANSI kleuren** voor terminal output
- **Emojis** voor visuele duidelijkheid
- **Nederlandse labels** en teksten
- **Kleurgecodeerde** confidence levels
- **Status indicatoren** met kleuren

### **✅ Tijdsperiode Opties**
- 📅 **Vandaag** - Predictions voor vandaag
- 🌅 **Gisteren** - Predictions van gisteren  
- 🌄 **Morgen** - Predictions voor morgen
- 📆 **Laatste 7 dagen** - Recente week
- 📊 **Laatste 30 dagen** - Recente maand
- 🔄 **Alle recente** - Alle beschikbare predictions

### **✅ Gedetailleerde Weergave**
- **Team informatie** met du<PERSON><PERSON><PERSON><PERSON> vs weergave
- **League details** per wedstrijd
- **Prediction details** met emojis
- **Score voorspellingen** met kleuren
- **Confidence levels** met kleurcodering
- **Resultaat vergelijking** (correct/incorrect)
- **Goals accuracy** analyse
- **Model informatie** en datum

### **✅ Statistieken Samenvatting**
- **Totaal predictions** count
- **Correcte predictions** count
- **Nauwkeurigheids percentage**
- **Performance rating** systeem
- **Kleurgecodeerde beoordelingen**

---

## 🚀 **Gebruik**

### **Toegang via Menu:**
```
Main Menu → Predictions → View Recent Predictions
```

### **Menu Navigatie:**
```
📊 PREDICTIONS OVERZICHT
═══════════════════════════════════════════════════════════════════

Kies een tijdsperiode:

  1. 📅 Vandaag
  2. 🌅 Gisteren
  3. 🌄 Morgen
  4. 📆 Laatste 7 dagen
  5. 📊 Laatste 30 dagen
  6. 🔄 Alle recente predictions

  0. ⬅️  Terug naar hoofdmenu

Voer je keuze in: 
```

---

## 🎨 **Kleur Systeem**

### **Confidence Levels:**
- 🟢 **Groen (≥70%)** - Hoge confidence, zeer betrouwbaar
- 🟡 **Geel (50-70%)** - Gemiddelde confidence, redelijk betrouwbaar  
- 🔴 **Rood (<50%)** - Lage confidence, minder betrouwbaar

### **Team Kleuren:**
- 🟢 **Groen** - Home team scores
- 🔵 **Blauw** - Away team scores
- ⚪ **Wit** - Team namen (highlighted)

### **Status Kleuren:**
- ✅ **Groen** - Correcte predictions
- ❌ **Rood** - Incorrecte predictions
- 🟡 **Geel** - Nog niet afgerond

### **Performance Ratings:**
- 🏆 **Groen** - Uitstekend (≥70% accuracy)
- ⭐ **Groen** - Zeer goed (60-70% accuracy)
- 👍 **Geel** - Goed (50-60% accuracy)
- 📈 **Rood** - Kan beter (<50% accuracy)

---

## 📊 **Voorbeeld Weergave**

```
📅 Vandaag
────────────────────────────────────────────────────────────

1. Ajax vs PSV
   🏆 Eredivisie

   🔮 Prediction: 🏠 Home Win
   ⚽ Score: 2.1 - 1.3 (totaal: 3.4)
   📊 Confidence: H:0.65 D:0.20 A:0.15
   🎯 Goals: Over 2.5: 0.72 | Under 2.5: 0.28
   
   🏆 Resultaat: 🏠 Home Win (✅ Correct)
   ⚽ Eindstand: 3 - 1
   🎯 Goals accuracy: ✅ Goed (verschil: 0.9, 0.3)
   
   🤖 Model: football_predictor_v20250529_093520 | Voorspeld op: 29-05-2024 14:30

📈 SAMENVATTING
────────────────────────────────────────
📊 Totaal predictions: 5
✅ Correcte predictions: 3
🎯 Nauwkeurigheid: 60.0%
🏅 Beoordeling: ⭐ Zeer goed
```

---

## 🔧 **Technische Details**

### **Module Structuur:**
```
ui/predictions_viewer.py
├── Colors class - ANSI kleur definities
├── view_recent_predictions_menu() - Hoofd menu functie
├── show_predictions_for_period() - Periode specifieke weergave
├── display_predictions_for_date() - Datum gegroepeerde weergave
├── display_single_prediction() - Individuele prediction weergave
└── display_summary_statistics() - Statistieken samenvatting
```

### **Database Queries:**
- **Complexe JOIN** queries voor complete prediction data
- **Datum filtering** voor verschillende tijdsperiodes
- **Optimized queries** voor performance
- **Null handling** voor missing data

### **Code Scheiding:**
- **Aparte module** (`predictions_viewer.py`) voor maintainability
- **Clean interface** met prediction_management.py
- **Modulaire functies** voor herbruikbaarheid
- **Consistent styling** en kleur systeem

---

## 🎯 **Voordelen**

### **Gebruikerservaring:**
- ✅ **Intuïtieve navigatie** met duidelijke menu opties
- ✅ **Visueel aantrekkelijk** met kleuren en emojis
- ✅ **Nederlandse interface** voor lokale gebruikers
- ✅ **Flexibele tijdsperiodes** voor verschillende behoeften
- ✅ **Gedetailleerde informatie** zonder overwhelming

### **Functionaliteit:**
- ✅ **Complete prediction data** in één overzicht
- ✅ **Accuracy tracking** voor performance monitoring
- ✅ **Confidence analysis** voor betrouwbaarheid
- ✅ **Goals accuracy** voor detailed analysis
- ✅ **Model versioning** voor traceability

### **Technisch:**
- ✅ **Modulaire architectuur** voor maintainability
- ✅ **Efficient database queries** voor performance
- ✅ **Error handling** voor robustness
- ✅ **Consistent styling** voor professional appearance
- ✅ **Extensible design** voor future enhancements

---

## 🚀 **Installatie & Setup**

### **Vereisten:**
- Python 3.8+
- Terminal met ANSI kleur support
- Database met predictions data
- Werkende football prediction system

### **Gebruik:**
```bash
# Via main application
python main.py
# → Predictions → View Recent Predictions

# Direct testen
python test_predictions_viewer.py
```

### **Configuratie:**
- **Automatische kleur detectie** voor terminal compatibility
- **Fallback opties** voor terminals zonder kleur support
- **Configureerbare tijdsperiodes** in code
- **Aanpasbare kleur schema's**

---

## 🎉 **Conclusie**

De nieuwe Predictions Viewer biedt een **professionele, gebruiksvriendelijke interface** voor het bekijken van football predictions met:

- 🎨 **Mooie kleurrijke weergave**
- 🇳🇱 **Nederlandse interface**
- ⏰ **Flexibele tijdsperiode opties**
- 📊 **Gedetailleerde statistieken**
- 🔧 **Modulaire code architectuur**

**Perfect voor dagelijks gebruik van het football prediction systeem!** ⚽🎯
