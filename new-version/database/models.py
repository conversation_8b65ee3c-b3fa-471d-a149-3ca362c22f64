"""
Database models and schemas for the Football Prediction System.
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, List
from enum import Enum


class MatchStatus(Enum):
    """Match status enumeration."""
    NOT_STARTED = "NS"
    FIRST_HALF = "1H"
    HALFTIME = "HT"
    SECOND_HALF = "2H"
    EXTRA_TIME = "ET"
    PENALTY_SHOOTOUT = "P"
    FINISHED = "FT"
    AFTER_EXTRA_TIME = "AET"
    AFTER_PENALTIES = "PEN"
    POSTPONED = "PST"
    CANCELLED = "CANC"
    SUSPENDED = "SUSP"
    INTERRUPTED = "INT"
    ABANDONED = "ABD"


class PredictionOutcome(Enum):
    """Prediction outcome enumeration."""
    HOME_WIN = 1
    DRAW = 0
    AWAY_WIN = 2


@dataclass
class Team:
    """Team model."""
    id: int
    name: str
    code: Optional[str] = None
    country: Optional[str] = None
    founded: Optional[int] = None
    logo: Optional[str] = None
    venue_id: Optional[int] = None
    venue_name: Optional[str] = None
    venue_capacity: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class League:
    """League model."""
    id: int
    name: str
    country: str
    season: int
    logo: Optional[str] = None
    flag: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    current: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class Match:
    """Completed match model."""
    id: int
    league_id: int
    season: int
    round: str
    date: datetime
    home_team_id: int
    away_team_id: int
    home_goals: Optional[int] = None
    away_goals: Optional[int] = None
    status: MatchStatus = MatchStatus.NOT_STARTED
    venue_id: Optional[int] = None
    referee: Optional[str] = None
    
    # Extended statistics
    home_shots: Optional[int] = None
    away_shots: Optional[int] = None
    home_shots_on_target: Optional[int] = None
    away_shots_on_target: Optional[int] = None
    home_possession: Optional[float] = None
    away_possession: Optional[float] = None
    home_corners: Optional[int] = None
    away_corners: Optional[int] = None
    home_fouls: Optional[int] = None
    away_fouls: Optional[int] = None
    home_yellow_cards: Optional[int] = None
    away_yellow_cards: Optional[int] = None
    home_red_cards: Optional[int] = None
    away_red_cards: Optional[int] = None
    
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @property
    def is_finished(self) -> bool:
        """Check if match is finished."""
        return self.status in [
            MatchStatus.FINISHED,
            MatchStatus.AFTER_EXTRA_TIME,
            MatchStatus.AFTER_PENALTIES
        ]
    
    @property
    def total_goals(self) -> Optional[int]:
        """Get total goals scored."""
        if self.home_goals is not None and self.away_goals is not None:
            return self.home_goals + self.away_goals
        return None
    
    @property
    def outcome(self) -> Optional[PredictionOutcome]:
        """Get match outcome."""
        if self.home_goals is not None and self.away_goals is not None:
            if self.home_goals > self.away_goals:
                return PredictionOutcome.HOME_WIN
            elif self.home_goals < self.away_goals:
                return PredictionOutcome.AWAY_WIN
            else:
                return PredictionOutcome.DRAW
        return None


@dataclass
class UpcomingGame:
    """Upcoming game model."""
    id: int
    league_id: int
    season: int
    round: str
    date: datetime
    home_team_id: int
    away_team_id: int
    status: MatchStatus = MatchStatus.NOT_STARTED
    venue_id: Optional[int] = None
    referee: Optional[str] = None
    
    # These fields are populated when the match is completed
    home_goals: Optional[int] = None
    away_goals: Optional[int] = None
    
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @property
    def is_finished(self) -> bool:
        """Check if game is finished."""
        return self.status in [
            MatchStatus.FINISHED,
            MatchStatus.AFTER_EXTRA_TIME,
            MatchStatus.AFTER_PENALTIES
        ]
    
    @property
    def can_be_moved_to_matches(self) -> bool:
        """Check if game can be moved to matches table."""
        return (self.is_finished and 
                self.home_goals is not None and 
                self.away_goals is not None)


@dataclass
class Prediction:
    """Prediction model."""
    id: Optional[int] = None
    match_id: int = 0
    model_version: str = ""
    prediction_date: Optional[datetime] = None
    
    # Predictions
    predicted_outcome: Optional[PredictionOutcome] = None
    predicted_home_goals: Optional[float] = None
    predicted_away_goals: Optional[float] = None
    predicted_total_goals: Optional[float] = None
    
    # Confidence scores
    confidence_home_win: Optional[float] = None
    confidence_draw: Optional[float] = None
    confidence_away_win: Optional[float] = None
    confidence_over_2_5: Optional[float] = None
    confidence_under_2_5: Optional[float] = None
    
    # Actual results (populated after match completion)
    actual_outcome: Optional[PredictionOutcome] = None
    actual_home_goals: Optional[int] = None
    actual_away_goals: Optional[int] = None
    actual_total_goals: Optional[int] = None
    
    # Accuracy metrics
    outcome_accuracy: Optional[float] = None
    goals_accuracy: Optional[float] = None
    total_accuracy: Optional[float] = None
    
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @property
    def has_actual_results(self) -> bool:
        """Check if prediction has actual results."""
        return (self.actual_outcome is not None and
                self.actual_home_goals is not None and
                self.actual_away_goals is not None)
    
    @property
    def is_outcome_correct(self) -> bool:
        """Check if outcome prediction is correct."""
        if self.predicted_outcome is not None and self.actual_outcome is not None:
            return self.predicted_outcome == self.actual_outcome
        return False
    
    def calculate_accuracy(self) -> None:
        """Calculate prediction accuracy metrics."""
        if not self.has_actual_results:
            return
        
        # Outcome accuracy
        self.outcome_accuracy = 1.0 if self.is_outcome_correct else 0.0
        
        # Goals accuracy (using mean absolute error)
        if (self.predicted_home_goals is not None and 
            self.predicted_away_goals is not None):
            home_error = abs(self.predicted_home_goals - self.actual_home_goals)
            away_error = abs(self.predicted_away_goals - self.actual_away_goals)
            max_error = max(home_error, away_error, 1.0)  # Avoid division by zero
            self.goals_accuracy = max(0.0, 1.0 - (home_error + away_error) / (2 * max_error))
        
        # Total accuracy (weighted combination)
        if self.outcome_accuracy is not None and self.goals_accuracy is not None:
            self.total_accuracy = (0.6 * self.outcome_accuracy + 0.4 * self.goals_accuracy)
