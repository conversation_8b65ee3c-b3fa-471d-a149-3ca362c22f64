"""
Database migrations and setup for the Football Prediction System.
"""
import logging
from typing import List
from .connection import get_db_cursor, test_connection

logger = logging.getLogger(__name__)


def create_teams_table() -> str:
    """Create teams table."""
    return """
    CREATE TABLE IF NOT EXISTS teams (
        id INTEGER PRIMARY KEY,
        name VA<PERSON>HA<PERSON>(255) NOT NULL,
        code VARCHAR(10),
        country VARCHAR(100),
        founded INTEGER,
        logo TEXT,
        venue_id INTEGER,
        venue_name VARCHAR(255),
        venue_capacity INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE INDEX IF NOT EXISTS idx_teams_name ON teams(name);
    CREATE INDEX IF NOT EXISTS idx_teams_country ON teams(country);
    """


def create_leagues_table() -> str:
    """Create leagues table."""
    return """
    CREATE TABLE IF NOT EXISTS leagues (
        id INTEGER NOT NULL,
        season INTEGER NOT NULL,
        name VARCHAR(255) NOT NULL,
        country VARCHAR(100) NOT NULL,
        logo TEXT,
        flag TEXT,
        start_date DATE,
        end_date DATE,
        current BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id, season)
    );

    CREATE INDEX IF NOT EXISTS idx_leagues_name ON leagues(name);
    CREATE INDEX IF NOT EXISTS idx_leagues_country ON leagues(country);
    CREATE INDEX IF NOT EXISTS idx_leagues_current ON leagues(current);
    """


def create_matches_table() -> str:
    """Create matches table for completed matches."""
    return """
    CREATE TABLE IF NOT EXISTS matches (
        id INTEGER PRIMARY KEY,
        league_id INTEGER NOT NULL,
        season INTEGER NOT NULL,
        round VARCHAR(100) NOT NULL,
        date TIMESTAMP NOT NULL,
        home_team_id INTEGER NOT NULL,
        away_team_id INTEGER NOT NULL,
        home_goals INTEGER,
        away_goals INTEGER,
        status VARCHAR(10) DEFAULT 'NS',
        venue_id INTEGER,
        referee VARCHAR(255),

        -- Extended statistics
        home_shots INTEGER,
        away_shots INTEGER,
        home_shots_on_target INTEGER,
        away_shots_on_target INTEGER,
        home_possession DECIMAL(5,2),
        away_possession DECIMAL(5,2),
        home_corners INTEGER,
        away_corners INTEGER,
        home_fouls INTEGER,
        away_fouls INTEGER,
        home_yellow_cards INTEGER DEFAULT 0,
        away_yellow_cards INTEGER DEFAULT 0,
        home_red_cards INTEGER DEFAULT 0,
        away_red_cards INTEGER DEFAULT 0,

        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (home_team_id) REFERENCES teams(id),
        FOREIGN KEY (away_team_id) REFERENCES teams(id),
        FOREIGN KEY (league_id, season) REFERENCES leagues(id, season)
    );

    CREATE INDEX IF NOT EXISTS idx_matches_date ON matches(date);
    CREATE INDEX IF NOT EXISTS idx_matches_league ON matches(league_id, season);
    CREATE INDEX IF NOT EXISTS idx_matches_teams ON matches(home_team_id, away_team_id);
    CREATE INDEX IF NOT EXISTS idx_matches_status ON matches(status);
    """


def create_upcoming_games_table() -> str:
    """Create upcoming_games table for matches to be played."""
    return """
    CREATE TABLE IF NOT EXISTS upcoming_games (
        id INTEGER PRIMARY KEY,
        league_id INTEGER NOT NULL,
        season INTEGER NOT NULL,
        round VARCHAR(100) NOT NULL,
        date TIMESTAMP NOT NULL,
        home_team_id INTEGER NOT NULL,
        away_team_id INTEGER NOT NULL,
        status VARCHAR(10) DEFAULT 'NS',
        venue_id INTEGER,
        referee VARCHAR(255),

        -- These are populated when match is completed
        home_goals INTEGER,
        away_goals INTEGER,

        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        FOREIGN KEY (home_team_id) REFERENCES teams(id),
        FOREIGN KEY (away_team_id) REFERENCES teams(id),
        FOREIGN KEY (league_id, season) REFERENCES leagues(id, season)
    );

    CREATE INDEX IF NOT EXISTS idx_upcoming_date ON upcoming_games(date);
    CREATE INDEX IF NOT EXISTS idx_upcoming_league ON upcoming_games(league_id, season);
    CREATE INDEX IF NOT EXISTS idx_upcoming_teams ON upcoming_games(home_team_id, away_team_id);
    CREATE INDEX IF NOT EXISTS idx_upcoming_status ON upcoming_games(status);
    """


def create_predictions_table() -> str:
    """Create predictions table."""
    return """
    CREATE TABLE IF NOT EXISTS predictions (
        id SERIAL PRIMARY KEY,
        match_id INTEGER NOT NULL,
        model_version VARCHAR(100) NOT NULL,
        prediction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        -- Predictions
        predicted_outcome INTEGER, -- 1=home_win, 0=draw, 2=away_win
        predicted_home_goals DECIMAL(4,2),
        predicted_away_goals DECIMAL(4,2),
        predicted_total_goals DECIMAL(4,2),

        -- Confidence scores (0.0 to 1.0)
        confidence_home_win DECIMAL(4,3),
        confidence_draw DECIMAL(4,3),
        confidence_away_win DECIMAL(4,3),
        confidence_over_2_5 DECIMAL(4,3),
        confidence_under_2_5 DECIMAL(4,3),

        -- Actual results (populated after match completion)
        actual_outcome INTEGER,
        actual_home_goals INTEGER,
        actual_away_goals INTEGER,
        actual_total_goals INTEGER,

        -- Accuracy metrics (0.0 to 1.0)
        outcome_accuracy DECIMAL(4,3),
        goals_accuracy DECIMAL(4,3),
        total_accuracy DECIMAL(4,3),

        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        UNIQUE(match_id, model_version)
    );

    CREATE INDEX IF NOT EXISTS idx_predictions_match ON predictions(match_id);
    CREATE INDEX IF NOT EXISTS idx_predictions_model ON predictions(model_version);
    CREATE INDEX IF NOT EXISTS idx_predictions_date ON predictions(prediction_date);
    CREATE INDEX IF NOT EXISTS idx_predictions_accuracy ON predictions(total_accuracy);
    """


def create_model_performance_table() -> str:
    """Create table to track model performance over time."""
    return """
    CREATE TABLE IF NOT EXISTS model_performance (
        id SERIAL PRIMARY KEY,
        model_version VARCHAR(100) NOT NULL,
        evaluation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        total_predictions INTEGER NOT NULL,
        correct_outcomes INTEGER NOT NULL,
        outcome_accuracy DECIMAL(5,4) NOT NULL,
        average_goals_accuracy DECIMAL(5,4),
        average_total_accuracy DECIMAL(5,4),
        confidence_calibration DECIMAL(5,4),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE INDEX IF NOT EXISTS idx_performance_model ON model_performance(model_version);
    CREATE INDEX IF NOT EXISTS idx_performance_date ON model_performance(evaluation_date);
    """


def create_models_table() -> str:
    """Create table to store trained models."""
    return """
    CREATE TABLE IF NOT EXISTS models (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        version VARCHAR(50) NOT NULL,
        model_type VARCHAR(50) NOT NULL,
        model_data BYTEA NOT NULL,
        feature_columns TEXT NOT NULL,
        training_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        training_samples INTEGER NOT NULL,
        validation_accuracy DECIMAL(5,4),
        is_active BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(name, version)
    );

    CREATE INDEX IF NOT EXISTS idx_models_name ON models(name);
    CREATE INDEX IF NOT EXISTS idx_models_active ON models(is_active);
    CREATE INDEX IF NOT EXISTS idx_models_type ON models(model_type);
    CREATE INDEX IF NOT EXISTS idx_models_name_version ON models(name, version);
    """


def create_learning_history_table() -> str:
    """Create table to track adaptive learning history."""
    return """
    CREATE TABLE IF NOT EXISTS learning_history (
        id SERIAL PRIMARY KEY,
        model_version VARCHAR(100) NOT NULL,
        learning_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        samples_used INTEGER NOT NULL,
        accuracy_before DECIMAL(5,4),
        accuracy_after DECIMAL(5,4),
        improvement DECIMAL(5,4),
        learning_reason TEXT,
        learning_parameters JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    CREATE INDEX IF NOT EXISTS idx_learning_model ON learning_history(model_version);
    CREATE INDEX IF NOT EXISTS idx_learning_date ON learning_history(learning_date);
    """


def get_migration_queries() -> List[str]:
    """Get all migration queries in order."""
    return [
        create_teams_table(),
        create_leagues_table(),
        create_matches_table(),
        create_upcoming_games_table(),
        create_predictions_table(),
        create_model_performance_table(),
        create_models_table(),
        create_learning_history_table()
    ]


def setup_database() -> bool:
    """
    Set up the database with all required tables.

    Returns:
        True if setup successful, False otherwise
    """
    try:
        # Test connection first
        if not test_connection():
            logger.error("Database connection test failed")
            return False

        logger.info("Setting up database tables...")

        # Execute all migration queries
        with get_db_cursor() as cursor:
            for query in get_migration_queries():
                cursor.execute(query)

        logger.info("Database setup completed successfully")
        return True

    except Exception as e:
        logger.error(f"Database setup failed: {e}")
        return False


def drop_all_tables() -> bool:
    """
    Drop all tables (use with caution!).

    Returns:
        True if successful, False otherwise
    """
    try:
        logger.warning("Dropping all database tables...")

        drop_queries = [
            "DROP TABLE IF EXISTS learning_history CASCADE;",
            "DROP TABLE IF EXISTS models CASCADE;",
            "DROP TABLE IF EXISTS model_performance CASCADE;",
            "DROP TABLE IF EXISTS predictions CASCADE;",
            "DROP TABLE IF EXISTS upcoming_games CASCADE;",
            "DROP TABLE IF EXISTS matches CASCADE;",
            "DROP TABLE IF EXISTS leagues CASCADE;",
            "DROP TABLE IF EXISTS teams CASCADE;"
        ]

        with get_db_cursor() as cursor:
            for query in drop_queries:
                cursor.execute(query)

        logger.info("All tables dropped successfully")
        return True

    except Exception as e:
        logger.error(f"Failed to drop tables: {e}")
        return False


def reset_database() -> bool:
    """
    Reset the database by dropping and recreating all tables.

    Returns:
        True if successful, False otherwise
    """
    logger.warning("Resetting database...")

    if not drop_all_tables():
        return False

    return setup_database()
