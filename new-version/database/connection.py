"""
Database connection management for the Football Prediction System.
"""
import psycopg2
import psycopg2.extras
import psycopg2.pool
from typing import Optional
import logging
from contextlib import contextmanager

from config import config

logger = logging.getLogger(__name__)

# Global connection pool
_connection_pool: Optional[psycopg2.pool.SimpleConnectionPool] = None


def initialize_connection_pool(min_connections: int = 1, max_connections: int = 10) -> None:
    """
    Initialize the database connection pool.

    Args:
        min_connections: Minimum number of connections in pool
        max_connections: Maximum number of connections in pool
    """
    global _connection_pool

    try:
        _connection_pool = psycopg2.pool.SimpleConnectionPool(
            min_connections,
            max_connections,
            host=config.database.host,
            port=config.database.port,
            database=config.database.database,
            user=config.database.username,
            password=config.database.password,
            cursor_factory=psycopg2.extras.RealDictCursor
        )
        logger.info("Database connection pool initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize connection pool: {e}")
        raise


def get_connection() -> psycopg2.extensions.connection:
    """
    Get a database connection from the pool.

    Returns:
        Database connection

    Raises:
        Exception: If connection pool is not initialized or connection fails
    """
    global _connection_pool

    if _connection_pool is None:
        initialize_connection_pool()

    try:
        connection = _connection_pool.getconn()
        if connection:
            return connection
        else:
            raise Exception("Failed to get connection from pool")
    except Exception as e:
        logger.error(f"Failed to get database connection: {e}")
        raise


def return_connection(connection: psycopg2.extensions.connection) -> None:
    """
    Return a connection to the pool.

    Args:
        connection: Database connection to return
    """
    global _connection_pool

    if _connection_pool and connection:
        _connection_pool.putconn(connection)


def close_connection_pool() -> None:
    """Close all connections in the pool."""
    global _connection_pool

    if _connection_pool:
        _connection_pool.closeall()
        _connection_pool = None
        logger.info("Database connection pool closed")


@contextmanager
def get_db_connection():
    """
    Context manager for database connections.

    Yields:
        Database connection
    """
    connection = None
    try:
        connection = get_connection()
        yield connection
    except Exception as e:
        if connection:
            connection.rollback()
        logger.error(f"Database operation failed: {e}")
        raise
    finally:
        if connection:
            return_connection(connection)


@contextmanager
def get_db_cursor(commit: bool = True):
    """
    Context manager for database cursors with automatic transaction handling.

    Args:
        commit: Whether to commit the transaction automatically

    Yields:
        Database cursor
    """
    connection = None
    cursor = None
    try:
        connection = get_connection()
        cursor = connection.cursor()
        yield cursor
        if commit:
            connection.commit()
    except Exception as e:
        if connection:
            connection.rollback()
        logger.error(f"Database operation failed: {e}")
        raise
    finally:
        if cursor:
            cursor.close()
        if connection:
            return_connection(connection)


def test_connection() -> bool:
    """
    Test the database connection.

    Returns:
        True if connection is successful, False otherwise
    """
    try:
        with get_db_cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            return result is not None
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False


def execute_query(query: str, params: tuple = None, fetch: bool = False) -> Optional[list]:
    """
    Execute a database query.

    Args:
        query: SQL query to execute
        params: Query parameters
        fetch: Whether to fetch results

    Returns:
        Query results if fetch=True, None otherwise
    """
    try:
        with get_db_cursor() as cursor:
            cursor.execute(query, params)
            if fetch:
                return cursor.fetchall()
            return None
    except Exception as e:
        logger.error(f"Query execution failed: {e}")
        raise


def execute_many(query: str, params_list: list) -> None:
    """
    Execute a query with multiple parameter sets.

    Args:
        query: SQL query to execute
        params_list: List of parameter tuples
    """
    try:
        with get_db_cursor() as cursor:
            cursor.executemany(query, params_list)
    except Exception as e:
        logger.error(f"Batch query execution failed: {e}")
        raise


# Alias for backward compatibility
def close_connection():
    """Alias for close_connection_pool."""
    close_connection_pool()
