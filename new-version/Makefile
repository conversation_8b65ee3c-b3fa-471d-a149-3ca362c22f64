# Football Prediction System - New Version
# Makefile for common development tasks

.PHONY: help install setup test run clean lint format

# Default target
help:
	@echo "Football Prediction System - Available Commands:"
	@echo ""
	@echo "  install     Install dependencies"
	@echo "  setup       Setup virtual environment and install dependencies"
	@echo "  setup-db    Setup database tables"
	@echo "  reset-db    Reset database (WARNING: deletes all data)"
	@echo "  test        Run tests"
	@echo "  run         Run the application"
	@echo "  clean       Clean up temporary files"
	@echo "  lint        Run code linting"
	@echo "  format      Format code"
	@echo ""

# Install dependencies
install:
	pip install -r requirements.txt

# Setup virtual environment and install dependencies
setup:
	python -m venv .venv
	@echo "Virtual environment created. Activate it with:"
	@echo "  source .venv/bin/activate  # On Linux/Mac"
	@echo "  .venv\\Scripts\\activate     # On Windows"
	@echo ""
	@echo "Then run: make install"

# Setup database
setup-db:
	python setup_database.py

# Reset database (dangerous!)
reset-db:
	@echo "WARNING: This will delete ALL data!"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	echo; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		python -c "from database.migrations import reset_database; reset_database()"; \
	fi

# Run tests
test:
	python -m pytest tests/ -v

# Run tests with coverage
test-coverage:
	python -m pytest tests/ -v --cov=. --cov-report=html

# Run the application
run:
	python main.py

# Clean up temporary files
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf dist/
	rm -rf build/

# Run code linting
lint:
	flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

# Format code
format:
	black . --line-length=100
	isort . --profile=black

# Type checking
typecheck:
	mypy . --ignore-missing-imports

# Install development dependencies
install-dev:
	pip install -r requirements.txt
	pip install pytest pytest-cov black flake8 isort mypy

# Full development setup
dev-setup: setup install-dev
	@echo "Development environment setup complete!"
	@echo "Don't forget to:"
	@echo "  1. Copy .env.example to .env and configure"
	@echo "  2. Run 'make setup-db' to initialize database"
	@echo "  3. Run 'make test' to verify everything works"

# Check if everything is working
check: lint typecheck test
	@echo "All checks passed! 🎉"
