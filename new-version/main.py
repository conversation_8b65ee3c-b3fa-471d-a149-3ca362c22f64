#!/usr/bin/env python3
"""
Football Prediction System - New Version
Main application entry point.
"""
import sys
import os
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from utils.logger import setup_logging
from database.connection import initialize_connection_pool, test_connection, close_connection_pool
from database.migrations import setup_database
from ui.main_menu import MainMenu


def setup_application() -> bool:
    """
    Set up the application environment.
    
    Returns:
        True if setup successful, False otherwise
    """
    try:
        # Setup logging
        setup_logging()
        logger = logging.getLogger(__name__)
        
        logger.info("Starting Football Prediction System - New Version")
        logger.info(f"Debug mode: {config.debug_mode}")
        
        # Create logs directory if it doesn't exist
        log_dir = Path(config.logging.file_path).parent
        log_dir.mkdir(exist_ok=True)
        
        # Initialize database connection pool
        logger.info("Initializing database connection...")
        initialize_connection_pool()
        
        # Test database connection
        if not test_connection():
            logger.error("Database connection test failed")
            return False
        
        logger.info("Database connection successful")
        
        # Check if database tables exist, create if needed
        logger.info("Checking database schema...")
        if not setup_database():
            logger.error("Database setup failed")
            return False
        
        logger.info("Application setup completed successfully")
        return True
        
    except Exception as e:
        print(f"Failed to setup application: {e}")
        return False


def cleanup_application():
    """Clean up application resources."""
    try:
        logger = logging.getLogger(__name__)
        logger.info("Cleaning up application resources...")
        
        # Close database connections
        close_connection_pool()
        
        logger.info("Application cleanup completed")
        
    except Exception as e:
        print(f"Error during cleanup: {e}")


def main():
    """Main application entry point."""
    try:
        # Setup application
        if not setup_application():
            print("❌ Failed to setup application. Please check the logs.")
            sys.exit(1)
        
        # Check API configuration
        if not config.api.football_api_key:
            print("⚠️  Warning: No Football API key configured.")
            print("   Some features may not work without API access.")
            print("   Set FOOTBALL_API_KEY environment variable or update config.py")
            print()
        
        # Start main menu
        print("🚀 Football Prediction System - New Version")
        print("=" * 50)
        
        main_menu = MainMenu()
        main_menu.run()
        
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye! Thanks for using Football Prediction System.")
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Unexpected error in main: {e}")
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
    finally:
        cleanup_application()


if __name__ == "__main__":
    main()
