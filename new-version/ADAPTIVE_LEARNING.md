# 🧠 Adaptive Learning System

Het **Adaptive Learning System** is een geavanceerd zelf-lerende systeem dat het voetbal voorspellingsmodel automatisch verbetert door te leren van zijn eigen voorspellingen.

## 🎯 Wat doet het?

Het systeem analyseert de prestaties van het model en past het automatisch aan wanneer:
- De nauwkeurigheid daalt
- Er nieuwe voorspellingsdata beschikbaar is
- Patronen in fouten worden gedetecteerd
- Confidence calibratie problemen optreden

## 🚀 Belangrijkste Features

### 1. **Intelligente Performance Analyse**
- Analyseert voorspellingsprestaties over verschillende tijdsperioden
- Detecteert trends en patronen in model prestaties
- Identificeert specifieke verbetergebieden
- Monitort confidence calibratie

### 2. **Smart Sample Selection**
- Selecteert de meest waardevolle samples voor training
- Prioriteert recente fouten en edge cases
- Balanceert verschillende uitkomstklassen
- Voorkomt overfitting door diverse sample selectie

### 3. **Adaptive Retraining**
- <PERSON>een retraining wanneer echt nodig
- Gebruikt verschillende strategieën gebaseerd op prestaties
- Behoudt goede modellen bij slechte nieuwe data
- Incrementele training voor efficiëntie

### 4. **Continuous Learning Cycle**
- Automatische monitoring van nieuwe voorspellingen
- Periodieke prestatie evaluatie
- Zelf-activerende verbeteringen
- Minimale menselijke interventie vereist

## 📊 Hoe werkt het?

### Performance Analysis
```python
# Analyseer prestaties van de laatste 30 dagen
learning_system = AdaptiveLearningSystem()
analysis = learning_system.analyze_prediction_performance(days_back=30)

print(f"Outcome accuracy: {analysis['outcome_accuracy']:.1%}")
print(f"Goals accuracy: {analysis['goals_accuracy']:.1%}")
print(f"High confidence accuracy: {analysis['high_confidence_accuracy']:.1%}")
```

### Intelligent Sample Selection
Het systeem scoort elke voorspelling op leerwaarde:
- **Recency**: Recente voorspellingen krijgen hogere prioriteit
- **Mistakes**: Foute voorspellingen zijn waardevoller voor leren
- **Overconfidence**: Hoge confidence fouten krijgen extra aandacht
- **Edge Cases**: Zeldzame uitkomsten (gelijkspel, hoge scores) worden geprioriteerd

### Adaptive Retraining
```python
# Automatische retraining gebaseerd op prestaties
results = run_adaptive_learning()

if results['learned']:
    print("Model verbeterd!")
    print(f"Samples gebruikt: {results['retrain_results']['samples_used']}")
else:
    print("Model prestaties zijn voldoende")
```

## 🎮 Gebruik via Menu

Ga naar **Prediction Management** → **🧠 Adaptive Learning System**:

1. **🔄 Run Continuous Learning Cycle** - Automatische verbetering
2. **📊 Analyze Current Performance** - Prestatie analyse
3. **🎯 Force Adaptive Retraining** - Geforceerde retraining
4. **📈 View Learning History** - Leergeschiedenis (coming soon)
5. **⚙️ Configure Learning Parameters** - Configuratie (coming soon)
6. **📚 Learn from Recent Predictions (Legacy)** - Oude methode

## 🔧 Programmatisch Gebruik

### Basis Gebruik
```python
from prediction.adaptive_learning import run_adaptive_learning

# Automatische verbetering
results = run_adaptive_learning()

if results['success']:
    if results['learned']:
        print("Model verbeterd!")
    else:
        print("Geen verbetering nodig")
```

### Geavanceerd Gebruik
```python
from prediction.adaptive_learning import AdaptiveLearningSystem

# Maak learning systeem met aangepaste parameters
learning_system = AdaptiveLearningSystem(
    learning_rate=0.1,                    # Hoe agressief te leren (0.0-1.0)
    confidence_threshold=0.8,             # Min confidence voor hoge kwaliteit
    min_accuracy_threshold=0.6,           # Min accuracy voor retraining trigger
    high_confidence_threshold=0.75        # Min high-confidence accuracy
)

# Laad model
if learning_system.load_model():
    print("Model geladen voor adaptive learning")

# Analyseer prestaties over verschillende perioden
for days in [7, 14, 30]:
    analysis = learning_system.analyze_prediction_performance(days_back=days)
    if analysis:
        print(f"Prestaties laatste {days} dagen:")
        print(f"  - Outcome accuracy: {analysis['outcome_accuracy']:.1%}")
        print(f"  - Goals accuracy: {analysis['goals_accuracy']:.1%}")
        print(f"  - High confidence accuracy: {analysis['high_confidence_accuracy']:.1%}")

# Intelligente sample selectie
samples = learning_system.intelligent_sample_selection(max_samples=50)
print(f"Geselecteerd {len(samples)} waardevolle samples voor training")

# Adaptive retraining met performance analyse
results = learning_system.adaptive_retraining()
if results['success']:
    if results.get('retrained', False):
        print("Model succesvol verbeterd!")
        print(f"Redenen: {results.get('retrain_reason', [])}")
    else:
        print("Model prestaties zijn voldoende")
```

### Scheduled Learning
```python
from prediction.adaptive_learning import schedule_adaptive_learning

# Voor gebruik in cron jobs
results = schedule_adaptive_learning()
```

## 📈 Performance Metrics

Het systeem monitort verschillende metrics:

### Accuracy Metrics
- **Outcome Accuracy**: Percentage correcte wedstrijduitkomsten
- **Goals Accuracy**: Percentage doelpunten binnen 1 goal tolerantie
- **High Confidence Accuracy**: Prestaties van hoge confidence voorspellingen

### Confidence Metrics
- **Confidence Calibration**: Hoe goed confidence percentages overeenkomen met werkelijke prestaties
- **Overconfidence Detection**: Identificeert wanneer het model te zeker is
- **Uncertainty Quantification**: Meet onzekerheid in voorspellingen

### Learning Metrics
- **Sample Quality**: Kwaliteit van geselecteerde training samples
- **Improvement Rate**: Snelheid van model verbetering
- **Stability**: Consistentie van model prestaties

## 🎯 Intelligente Strategieën

### 1. **Performance-Based Learning Rate**
- Slechte prestaties → Agressievere learning
- Goede prestaties → Conservatievere learning
- Balanceert verbetering vs stabiliteit

### 2. **Quality Sample Enhancement**
- Voegt hoge kwaliteit samples toe aan training
- Voorkomt degradatie door slechte nieuwe data
- Behoudt model stabiliteit

### 3. **Class Balancing**
- Zorgt voor gebalanceerde uitkomstklassen
- Voorkomt bias naar specifieke uitkomsten
- Verbetert generalisatie

### 4. **Confidence Protection**
- Beschermt hoge confidence voorspellingen
- Voorkomt onnodige overschrijving
- Behoudt model zekerheid

## 🔄 Continuous Learning Workflow

1. **Data Collection**: Verzamel nieuwe voorspellingsresultaten
2. **Performance Analysis**: Analyseer huidige model prestaties
3. **Decision Making**: Bepaal of retraining nodig is
4. **Sample Selection**: Selecteer meest waardevolle training data
5. **Adaptive Training**: Train model met intelligente strategieën
6. **Validation**: Valideer verbeteringen
7. **Model Update**: Sla verbeterd model op

## 🚀 Voordelen

### Voor Gebruikers
- **Automatische Verbetering**: Model wordt vanzelf beter
- **Minimale Interventie**: Weinig handmatige actie vereist
- **Transparante Prestaties**: Duidelijke metrics en feedback
- **Betrouwbare Voorspellingen**: Consistente kwaliteit

### Voor Ontwikkelaars
- **Modulair Design**: Gemakkelijk uit te breiden
- **Configureerbaar**: Aanpasbare parameters
- **Goed Gelogd**: Uitgebreide logging voor debugging
- **Testbaar**: Volledige test suite beschikbaar

## 🧪 Testing

Run de test suite:
```bash
python test_adaptive_learning.py
```

Dit test:
- Performance analysis functionaliteit
- Intelligent sample selection
- Continuous learning cycle
- Forced retraining

## 🔮 Toekomstige Uitbreidingen

- **Learning History Tracking**: Volledige geschiedenis van model verbeteringen
- **Parameter Auto-tuning**: Automatische optimalisatie van learning parameters
- **Multi-model Learning**: Leren tussen verschillende model types
- **Advanced Metrics**: Meer geavanceerde prestatie metrics
- **Real-time Learning**: Live learning tijdens wedstrijden

## ⚙️ Configuratie Parameters

Het systeem kan worden aangepast via verschillende parameters:

### **Core Parameters**
- **`learning_rate`** (0.0-1.0): Hoe agressief het systeem leert
  - `0.05`: Zeer conservatief, kleine aanpassingen
  - `0.1`: Standaard, gebalanceerd leren
  - `0.2`: Agressief, snelle aanpassingen

- **`confidence_threshold`** (0.0-1.0): Minimum confidence voor hoge kwaliteit samples
  - `0.7`: Lage drempel, meer samples
  - `0.8`: Standaard, gebalanceerd
  - `0.9`: Hoge drempel, alleen zeer zekere samples

### **Performance Thresholds**
- **`min_accuracy_threshold`** (0.0-1.0): Minimum accuracy voordat retraining wordt getriggerd
  - `0.5`: Lage drempel, frequente retraining
  - `0.6`: Standaard
  - `0.7`: Hoge drempel, minder frequente retraining

- **`high_confidence_threshold`** (0.0-1.0): Minimum accuracy voor hoge confidence voorspellingen
  - `0.7`: Lage verwachting
  - `0.75`: Standaard
  - `0.85`: Hoge verwachting

### **Voorbeeld Configuraties**

#### Conservatieve Setup (Stabiel)
```python
learning_system = AdaptiveLearningSystem(
    learning_rate=0.05,
    confidence_threshold=0.85,
    min_accuracy_threshold=0.7,
    high_confidence_threshold=0.8
)
```

#### Agressieve Setup (Snelle Verbetering)
```python
learning_system = AdaptiveLearningSystem(
    learning_rate=0.2,
    confidence_threshold=0.7,
    min_accuracy_threshold=0.5,
    high_confidence_threshold=0.7
)
```

#### Experimentele Setup (Maximaal Leren)
```python
learning_system = AdaptiveLearningSystem(
    learning_rate=0.3,
    confidence_threshold=0.6,
    min_accuracy_threshold=0.4,
    high_confidence_threshold=0.6
)
```

## 💡 Tips voor Optimaal Gebruik

1. **Regelmatige Monitoring**: Check prestaties wekelijks
2. **Genoeg Data**: Zorg voor voldoende voorspellingsdata (minimaal 20-30 matches)
3. **Geduld**: Laat het systeem tijd om te leren (2-3 weken voor merkbare verbetering)
4. **Feedback**: Monitor de learning results en pas parameters aan
5. **Backup**: Bewaar goede modellen als backup voordat je experimenteert
6. **Graduele Aanpassing**: Begin conservatief en verhoog agressiviteit geleidelijk
7. **Seizoen Awareness**: Pas parameters aan voor begin/eind seizoen verschillen

## 🚨 Waarschuwingen

- **Overfitting**: Te agressieve parameters kunnen leiden tot overfitting
- **Data Quality**: Slechte input data kan model prestaties verslechteren
- **Backup Models**: Bewaar altijd een werkend model voordat je experimenteert
- **Performance Monitoring**: Monitor altijd of verbeteringen echt verbetering zijn

Het Adaptive Learning System maakt je voetbal voorspellingsmodel intelligenter en betrouwbaarder door continu te leren van zijn eigen prestaties! 🚀⚽
