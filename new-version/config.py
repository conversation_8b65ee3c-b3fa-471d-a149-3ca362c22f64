"""
Configuration settings for the Football Prediction System.
"""
import os
from typing import Dict, Any
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    host: str = "localhost"
    port: int = 5432
    database: str = "football_predictions"
    username: str = "postgres"
    password: str = "password"

    @property
    def connection_string(self) -> str:
        """Get PostgreSQL connection string."""
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"


@dataclass
class ApiConfig:
    """API configuration settings."""
    football_api_key: str = ""
    football_api_host: str = "v3.football.api-sports.io"
    base_url: str = "https://v3.football.api-sports.io"
    rate_limit_delay: float = 1.0  # seconds between requests
    timeout: int = 30  # request timeout in seconds


@dataclass
class ModelConfig:
    """Machine learning model configuration."""
    default_model_type: str = "random_forest"
    test_size: float = 0.2
    random_state: int = 42
    cross_validation_folds: int = 5

    # Model parameters
    random_forest_params: Dict[str, Any] = None
    xgboost_params: Dict[str, Any] = None
    lightgbm_params: Dict[str, Any] = None

    def __post_init__(self):
        """Initialize default model parameters."""
        if self.random_forest_params is None:
            self.random_forest_params = {
                'n_estimators': 100,
                'max_depth': 10,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': self.random_state
            }

        if self.xgboost_params is None:
            self.xgboost_params = {
                'n_estimators': 100,
                'max_depth': 6,
                'learning_rate': 0.1,
                'random_state': self.random_state
            }

        if self.lightgbm_params is None:
            self.lightgbm_params = {
                'n_estimators': 100,
                'max_depth': 6,
                'learning_rate': 0.1,
                'random_state': self.random_state
            }


@dataclass
class AdaptiveLearningConfig:
    """Adaptive learning system configuration."""
    min_predictions_for_learning: int = 10
    performance_threshold: float = 0.6  # Minimum accuracy to maintain
    retraining_threshold: float = 0.05  # Performance drop that triggers retraining
    learning_cycle_interval: int = 24  # Hours between automatic learning cycles
    max_learning_data_age: int = 30  # Days of data to use for learning
    confidence_threshold: float = 0.7  # Minimum confidence for predictions


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str = "logs/football_predictions.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


@dataclass
class AppConfig:
    """Main application configuration."""
    database: DatabaseConfig
    api: ApiConfig
    model: ModelConfig
    adaptive_learning: AdaptiveLearningConfig
    logging: LoggingConfig

    # Application settings
    cache_enabled: bool = True
    cache_ttl: int = 3600  # Cache time-to-live in seconds
    debug_mode: bool = False


def load_config() -> AppConfig:
    """
    Load configuration from environment variables and defaults.

    Returns:
        AppConfig: Complete application configuration
    """
    # Database configuration
    database = DatabaseConfig(
        host=os.getenv("DB_HOST", "localhost"),
        port=int(os.getenv("DB_PORT", "5432")),
        database=os.getenv("DB_NAME", "football_predictions"),
        username=os.getenv("DB_USER", "postgres"),
        password=os.getenv("DB_PASSWORD", "password")
    )

    # API configuration
    api = ApiConfig(
        football_api_key=os.getenv("FOOTBALL_API_KEY", ""),
        football_api_host=os.getenv("FOOTBALL_API_HOST", "v3.football.api-sports.io"),
        base_url=os.getenv("FOOTBALL_API_URL", "https://v3.football.api-sports.io"),
        rate_limit_delay=float(os.getenv("API_RATE_LIMIT", "1.0")),
        timeout=int(os.getenv("API_TIMEOUT", "30"))
    )

    # Model configuration
    model = ModelConfig(
        default_model_type=os.getenv("DEFAULT_MODEL_TYPE", "random_forest"),
        test_size=float(os.getenv("MODEL_TEST_SIZE", "0.2")),
        random_state=int(os.getenv("MODEL_RANDOM_STATE", "42"))
    )

    # Adaptive learning configuration
    adaptive_learning = AdaptiveLearningConfig(
        min_predictions_for_learning=int(os.getenv("MIN_PREDICTIONS_LEARNING", "10")),
        performance_threshold=float(os.getenv("PERFORMANCE_THRESHOLD", "0.6")),
        retraining_threshold=float(os.getenv("RETRAINING_THRESHOLD", "0.05")),
        learning_cycle_interval=int(os.getenv("LEARNING_CYCLE_INTERVAL", "24")),
        max_learning_data_age=int(os.getenv("MAX_LEARNING_DATA_AGE", "30")),
        confidence_threshold=float(os.getenv("CONFIDENCE_THRESHOLD", "0.7"))
    )

    # Logging configuration
    logging_config = LoggingConfig(
        level=os.getenv("LOG_LEVEL", "INFO"),
        file_path=os.getenv("LOG_FILE", "logs/football_predictions.log")
    )

    # Main application configuration
    return AppConfig(
        database=database,
        api=api,
        model=model,
        adaptive_learning=adaptive_learning,
        logging=logging_config,
        cache_enabled=os.getenv("CACHE_ENABLED", "true").lower() == "true",
        cache_ttl=int(os.getenv("CACHE_TTL", "3600")),
        debug_mode=os.getenv("DEBUG_MODE", "false").lower() == "true"
    )


# Global configuration instance
config = load_config()
