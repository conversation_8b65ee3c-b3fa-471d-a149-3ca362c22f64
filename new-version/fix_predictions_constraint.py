#!/usr/bin/env python3
"""
Fix the predictions table constraint to enable ON CONFLICT functionality.
"""
import sys
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.connection import initialize_connection_pool, get_db_cursor, close_connection_pool
from utils.logger import setup_logging

logger = logging.getLogger(__name__)


def check_predictions_table_constraints():
    """Check current constraints on the predictions table."""
    try:
        logger.info("🔍 Checking predictions table constraints...")
        
        with get_db_cursor() as cursor:
            # Check if predictions table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'predictions'
                )
            """)
            table_exists = cursor.fetchone()[0]
            
            if not table_exists:
                logger.warning("⚠️ Predictions table does not exist")
                return False
            
            # Check current constraints
            cursor.execute("""
                SELECT constraint_name, constraint_type
                FROM information_schema.table_constraints 
                WHERE table_name = 'predictions' 
                AND constraint_type = 'UNIQUE'
            """)
            
            constraints = cursor.fetchall()
            
            logger.info("📋 Current unique constraints on predictions table:")
            if constraints:
                for constraint in constraints:
                    logger.info(f"   • {constraint['constraint_name']}: {constraint['constraint_type']}")
            else:
                logger.info("   • No unique constraints found")
            
            # Check if the specific constraint we need exists
            cursor.execute("""
                SELECT constraint_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu 
                    ON tc.constraint_name = kcu.constraint_name
                WHERE tc.table_name = 'predictions' 
                AND tc.constraint_type = 'UNIQUE'
                AND kcu.column_name IN ('match_id', 'model_version')
                GROUP BY constraint_name
                HAVING COUNT(*) = 2
            """)
            
            needed_constraint = cursor.fetchone()
            
            if needed_constraint:
                logger.info(f"✅ Required constraint exists: {needed_constraint['constraint_name']}")
                return True
            else:
                logger.info("❌ Required UNIQUE(match_id, model_version) constraint missing")
                return False
                
    except Exception as e:
        logger.error(f"❌ Error checking predictions table constraints: {e}")
        return False


def add_predictions_unique_constraint():
    """Add the missing unique constraint to the predictions table."""
    try:
        logger.info("🔧 Adding unique constraint to predictions table...")
        
        with get_db_cursor() as cursor:
            # Check for existing duplicate data that would prevent constraint creation
            logger.info("   🔍 Checking for duplicate predictions...")
            cursor.execute("""
                SELECT match_id, model_version, COUNT(*) as count
                FROM predictions
                GROUP BY match_id, model_version
                HAVING COUNT(*) > 1
            """)
            
            duplicates = cursor.fetchall()
            
            if duplicates:
                logger.warning(f"⚠️ Found {len(duplicates)} duplicate prediction combinations")
                logger.info("   🧹 Cleaning up duplicates (keeping most recent)...")
                
                for dup in duplicates:
                    # Keep only the most recent prediction for each match_id, model_version combination
                    cursor.execute("""
                        DELETE FROM predictions 
                        WHERE match_id = %s AND model_version = %s
                        AND id NOT IN (
                            SELECT id FROM (
                                SELECT id FROM predictions 
                                WHERE match_id = %s AND model_version = %s
                                ORDER BY created_at DESC 
                                LIMIT 1
                            ) as keep
                        )
                    """, (dup['match_id'], dup['model_version'], 
                         dup['match_id'], dup['model_version']))
                    
                    deleted_count = cursor.rowcount
                    logger.info(f"   🗑️ Removed {deleted_count} duplicate predictions for match {dup['match_id']}")
            else:
                logger.info("   ✅ No duplicate predictions found")
            
            # Add the unique constraint
            logger.info("   🔧 Adding UNIQUE(match_id, model_version) constraint...")
            cursor.execute("""
                ALTER TABLE predictions 
                ADD CONSTRAINT predictions_match_model_unique 
                UNIQUE (match_id, model_version)
            """)
            
            logger.info("   ✅ Unique constraint added successfully")
            
        logger.info("✅ Predictions table constraint fix completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error adding predictions unique constraint: {e}")
        import traceback
        logger.error(f"📋 Full traceback: {traceback.format_exc()}")
        return False


def test_predictions_save_functionality():
    """Test that predictions can now be saved with ON CONFLICT."""
    try:
        logger.info("🧪 Testing predictions save functionality...")
        
        with get_db_cursor() as cursor:
            # Test data
            test_match_id = 999999
            test_model_version = "test_model_v1"
            
            # Clean up any existing test data
            cursor.execute("""
                DELETE FROM predictions 
                WHERE match_id = %s AND model_version = %s
            """, (test_match_id, test_model_version))
            
            # Test 1: Insert new prediction
            logger.info("   📝 Test 1: Inserting new prediction...")
            cursor.execute("""
                INSERT INTO predictions (
                    match_id, model_version, predicted_outcome,
                    predicted_home_goals, predicted_away_goals, predicted_total_goals,
                    confidence_home_win, confidence_draw, confidence_away_win,
                    confidence_over_2_5, confidence_under_2_5
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (match_id, model_version) DO UPDATE SET
                    predicted_outcome = EXCLUDED.predicted_outcome,
                    updated_at = NOW()
            """, (test_match_id, test_model_version, 1, 2.0, 1.0, 3.0, 0.6, 0.2, 0.2, 0.7, 0.3))
            
            logger.info("   ✅ Test 1 passed: New prediction inserted")
            
            # Test 2: Update existing prediction (ON CONFLICT)
            logger.info("   🔄 Test 2: Updating existing prediction...")
            cursor.execute("""
                INSERT INTO predictions (
                    match_id, model_version, predicted_outcome,
                    predicted_home_goals, predicted_away_goals, predicted_total_goals,
                    confidence_home_win, confidence_draw, confidence_away_win,
                    confidence_over_2_5, confidence_under_2_5
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (match_id, model_version) DO UPDATE SET
                    predicted_outcome = EXCLUDED.predicted_outcome,
                    predicted_home_goals = EXCLUDED.predicted_home_goals,
                    updated_at = NOW()
            """, (test_match_id, test_model_version, 2, 1.5, 2.5, 4.0, 0.3, 0.2, 0.5, 0.8, 0.2))
            
            logger.info("   ✅ Test 2 passed: Existing prediction updated")
            
            # Verify the update worked
            cursor.execute("""
                SELECT predicted_outcome, predicted_home_goals, predicted_away_goals
                FROM predictions 
                WHERE match_id = %s AND model_version = %s
            """, (test_match_id, test_model_version))
            
            result = cursor.fetchone()
            if result and result['predicted_outcome'] == 2:
                logger.info("   ✅ Test 3 passed: Update verification successful")
            else:
                logger.error("   ❌ Test 3 failed: Update verification failed")
                return False
            
            # Clean up test data
            cursor.execute("""
                DELETE FROM predictions 
                WHERE match_id = %s AND model_version = %s
            """, (test_match_id, test_model_version))
            
            logger.info("   🧹 Test data cleaned up")
            
        logger.info("✅ All prediction save tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Prediction save test failed: {e}")
        import traceback
        logger.error(f"📋 Full traceback: {traceback.format_exc()}")
        return False


def main():
    """Main function to fix predictions table constraints."""
    print("🔧 Predictions Table Constraint Fix")
    print("=" * 50)
    
    try:
        # Setup
        setup_logging()
        initialize_connection_pool()
        
        # Step 1: Check current constraints
        print("\n📋 Step 1: Checking current constraints...")
        constraint_exists = check_predictions_table_constraints()
        
        if constraint_exists:
            print("✅ Required constraint already exists!")
        else:
            print("❌ Required constraint missing - proceeding with fix...")
            
            # Step 2: Add missing constraint
            print("\n🔧 Step 2: Adding missing constraint...")
            if add_predictions_unique_constraint():
                print("✅ Constraint added successfully!")
            else:
                print("❌ Failed to add constraint!")
                return False
        
        # Step 3: Test functionality
        print("\n🧪 Step 3: Testing prediction save functionality...")
        if test_predictions_save_functionality():
            print("✅ Prediction save functionality working!")
        else:
            print("❌ Prediction save functionality failed!")
            return False
        
        # Success summary
        print("\n🎉 SUCCESS: Predictions constraint fix completed!")
        print("\n📊 What was fixed:")
        print("   • Added UNIQUE(match_id, model_version) constraint to predictions table")
        print("   • Cleaned up any duplicate prediction data")
        print("   • Verified ON CONFLICT functionality works correctly")
        print("\n🚀 Result:")
        print("   • Predictions can now be saved successfully")
        print("   • ON CONFLICT DO UPDATE works for prediction updates")
        print("   • Database integrity maintained")
        print("\n✅ Your prediction system is now fully functional!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        close_connection_pool()


if __name__ == "__main__":
    main()
