#!/usr/bin/env python3
"""
Test script voor de nieuwe mooie predictions viewer.
"""
import sys
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.connection import initialize_connection_pool, close_connection_pool
from ui.predictions_viewer import view_recent_predictions_menu
from utils.logger import setup_logging

logger = logging.getLogger(__name__)


def test_predictions_viewer():
    """Test de nieuwe predictions viewer."""
    try:
        logger.info("🧪 Testing nieuwe predictions viewer...")
        
        print("🎯 PREDICTIONS VIEWER TEST")
        print("=" * 50)
        print()
        print("Dit test script laat de nieuwe mooie predictions viewer zien.")
        print("De viewer heeft de volgende features:")
        print()
        print("✅ Kleurrijke interface met emojis")
        print("✅ Nederlandse labels en teksten")
        print("✅ Verschillende tijdsperiode opties:")
        print("   • Vandaag")
        print("   • Gisteren") 
        print("   • Morgen")
        print("   • Laatste 7 dagen")
        print("   • Laatste 30 dagen")
        print("   • Alle recente predictions")
        print()
        print("✅ Mooie weergave van:")
        print("   • Team namen met kleuren")
        print("   • Prediction details met emojis")
        print("   • Confidence levels met kleurcodering")
        print("   • Resultaten met status (correct/incorrect)")
        print("   • Samenvatting statistieken")
        print()
        
        confirm = input("Wil je de predictions viewer testen? (y/n): ").lower()
        if confirm == 'y':
            print("\n🚀 Starting predictions viewer...")
            view_recent_predictions_menu()
        else:
            print("❌ Test geannuleerd.")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing predictions viewer: {e}")
        import traceback
        logger.error(f"📋 Full traceback: {traceback.format_exc()}")
        return False


def show_color_demo():
    """Laat een demo zien van de kleuren die gebruikt worden."""
    from ui.predictions_viewer import Colors, colorize
    
    print("\n🎨 KLEUR DEMO")
    print("=" * 30)
    print()
    
    print("Tekst kleuren:")
    print(f"  {colorize('✅ Groen (correct predictions)', Colors.BRIGHT_GREEN)}")
    print(f"  {colorize('❌ Rood (incorrect predictions)', Colors.BRIGHT_RED)}")
    print(f"  {colorize('🟡 Geel (gemiddelde confidence)', Colors.YELLOW)}")
    print(f"  {colorize('🔵 Blauw (away team)', Colors.BLUE)}")
    print(f"  {colorize('🟢 Groen (home team)', Colors.GREEN)}")
    print(f"  {colorize('🟣 Magenta (tijdsperiodes)', Colors.MAGENTA)}")
    print(f"  {colorize('🔷 Cyan (headers)', Colors.CYAN)}")
    print()
    
    print("Confidence level kleuren:")
    print(f"  {colorize('Hoge confidence (≥70%)', Colors.BRIGHT_GREEN)} - Zeer betrouwbaar")
    print(f"  {colorize('Gemiddelde confidence (50-70%)', Colors.YELLOW)} - Redelijk betrouwbaar")
    print(f"  {colorize('Lage confidence (<50%)', Colors.RED)} - Minder betrouwbaar")
    print()
    
    print("Status indicatoren:")
    print(f"  {colorize('🏆 Uitstekend (≥70% accuracy)', Colors.BRIGHT_GREEN)}")
    print(f"  {colorize('⭐ Zeer goed (60-70% accuracy)', Colors.GREEN)}")
    print(f"  {colorize('👍 Goed (50-60% accuracy)', Colors.YELLOW)}")
    print(f"  {colorize('📈 Kan beter (<50% accuracy)', Colors.RED)}")
    print()


def show_features_overview():
    """Laat een overzicht zien van alle features."""
    print("\n📋 FEATURES OVERZICHT")
    print("=" * 40)
    print()
    
    print("🎯 MENU OPTIES:")
    print("  📅 Vandaag - Predictions voor vandaag")
    print("  🌅 Gisteren - Predictions van gisteren")
    print("  🌄 Morgen - Predictions voor morgen")
    print("  📆 Laatste 7 dagen - Recente week")
    print("  📊 Laatste 30 dagen - Recente maand")
    print("  🔄 Alle recente - Alle beschikbare predictions")
    print()
    
    print("🎨 DISPLAY FEATURES:")
    print("  ✅ Kleurrijke interface met ANSI kleuren")
    print("  ✅ Nederlandse labels en teksten")
    print("  ✅ Emojis voor visuele duidelijkheid")
    print("  ✅ Gegroepeerd per datum")
    print("  ✅ Duidelijke team vs team weergave")
    print("  ✅ Confidence levels met kleurcodering")
    print("  ✅ Resultaat vergelijking (correct/incorrect)")
    print("  ✅ Goals accuracy analyse")
    print("  ✅ Samenvatting statistieken")
    print("  ✅ Performance rating systeem")
    print()
    
    print("📊 INFORMATIE PER PREDICTION:")
    print("  🏠 Home team vs ✈️ Away team")
    print("  🏆 League informatie")
    print("  🔮 Prediction (Home Win/Draw/Away Win)")
    print("  ⚽ Score voorspelling")
    print("  📊 Confidence percentages")
    print("  🎯 Over/Under 2.5 goals")
    print("  🏆 Werkelijke resultaat (indien beschikbaar)")
    print("  ✅/❌ Correctheid status")
    print("  🤖 Model versie en datum")
    print()
    
    print("📈 STATISTIEKEN:")
    print("  📊 Totaal aantal predictions")
    print("  ✅ Aantal correcte predictions")
    print("  🎯 Nauwkeurigheids percentage")
    print("  🏅 Performance beoordeling")
    print("  🎨 Kleurgecodeerde ratings")
    print()


def main():
    """Main functie voor het testen van de predictions viewer."""
    print("🎯 PREDICTIONS VIEWER TEST SUITE")
    print("=" * 50)
    
    try:
        # Setup
        setup_logging()
        initialize_connection_pool()
        
        # Show features
        show_features_overview()
        show_color_demo()
        
        # Test the viewer
        if test_predictions_viewer():
            print("\n✅ SUCCESS: Predictions viewer test completed!")
            print("\n🎉 De nieuwe predictions viewer is klaar voor gebruik!")
            print("\nFeatures:")
            print("  ✅ Mooie kleurrijke interface")
            print("  ✅ Nederlandse labels")
            print("  ✅ Verschillende tijdsperiode opties")
            print("  ✅ Duidelijke prediction weergave")
            print("  ✅ Gedetailleerde statistieken")
            print("  ✅ Aparte module voor code scheiding")
        else:
            print("\n❌ FAILED: Issues with predictions viewer test")
        
        return True
        
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        close_connection_pool()


if __name__ == "__main__":
    main()
