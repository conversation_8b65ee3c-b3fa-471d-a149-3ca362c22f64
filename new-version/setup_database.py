#!/usr/bin/env python3
"""
Database setup script for the Football Prediction System.
"""
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from utils.logger import setup_logging
from database.connection import initialize_connection_pool, test_connection, close_connection_pool
from database.migrations import setup_database, drop_all_tables, reset_database


def main():
    """Main setup function."""
    print("🚀 Football Prediction System - Database Setup")
    print("=" * 50)
    
    # Setup logging
    setup_logging()
    
    # Create logs directory
    log_dir = Path(config.logging.file_path).parent
    log_dir.mkdir(exist_ok=True)
    
    print(f"\n📋 Database Configuration:")
    print(f"   Host: {config.database.host}")
    print(f"   Port: {config.database.port}")
    print(f"   Database: {config.database.database}")
    print(f"   Username: {config.database.username}")
    
    try:
        # Initialize connection pool
        print(f"\n🔌 Connecting to database...")
        initialize_connection_pool()
        
        # Test connection
        if not test_connection():
            print("❌ Database connection failed!")
            print("\nPlease check:")
            print("  • Database server is running")
            print("  • Connection settings in config.py")
            print("  • Database exists and user has permissions")
            return False
        
        print("✅ Database connection successful!")
        
        # Ask user what to do
        print(f"\n📋 Setup Options:")
        print("  1. Setup new database (create tables)")
        print("  2. Reset database (drop and recreate all tables)")
        print("  3. Test connection only")
        print("  0. Exit")
        
        choice = input(f"\nEnter your choice (1-3, 0 to exit): ").strip()
        
        if choice == '1':
            print(f"\n🔧 Setting up database tables...")
            if setup_database():
                print("✅ Database setup completed successfully!")
                print("\nYou can now run the main application:")
                print("  python main.py")
            else:
                print("❌ Database setup failed!")
                return False
                
        elif choice == '2':
            print(f"\n⚠️  WARNING: This will delete ALL data in the database!")
            confirm = input("Are you sure? Type 'yes' to confirm: ").strip().lower()
            
            if confirm == 'yes':
                print(f"\n🔧 Resetting database...")
                if reset_database():
                    print("✅ Database reset completed successfully!")
                else:
                    print("❌ Database reset failed!")
                    return False
            else:
                print("❌ Database reset cancelled.")
                
        elif choice == '3':
            print(f"\n✅ Connection test completed successfully!")
            
        elif choice == '0':
            print(f"\n👋 Goodbye!")
            
        else:
            print(f"❌ Invalid choice.")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
        
    finally:
        close_connection_pool()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
