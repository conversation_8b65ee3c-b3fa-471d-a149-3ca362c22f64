#!/usr/bin/env python3
"""
Test script to verify both fixes work correctly:
1. Color display fix in predictions viewer
2. Intelligent null data handling in model training
"""
import sys
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.connection import initialize_connection_pool, close_connection_pool
from ui.predictions_viewer import view_recent_predictions_menu, Colors
from ml.intelligent_data_handler import IntelligentDataHandler
from ml.model_manager import ModelManager
from utils.logger import setup_logging

logger = logging.getLogger(__name__)


def test_color_display_fix():
    """Test that the color display error is fixed."""
    try:
        logger.info("🎨 Testing color display fix...")
        
        # Test that all color attributes exist
        required_colors = [
            'RESET', 'BOLD', 'DIM', 'RED', 'GREEN', 'YELLOW', 'BLUE', 'MAGENTA', 'CYAN', 'WHITE',
            'BG_RED', 'BG_GREEN', 'BG_YELLOW', 'BG_BLUE', 'BG_MAGENTA', 'BG_CYAN',
            'BRIGHT_RED', 'BRIGHT_GREEN', 'BRIGHT_YELLOW', 'BRIGHT_BLUE', 'BRIGHT_MAGENTA', 
            'BRIGHT_CYAN', 'BRIGHT_WHITE', 'ORANGE'
        ]
        
        missing_colors = []
        for color in required_colors:
            if not hasattr(Colors, color):
                missing_colors.append(color)
        
        if missing_colors:
            logger.error(f"❌ Missing color attributes: {missing_colors}")
            return False
        
        # Test color usage
        from ui.predictions_viewer import colorize
        test_text = colorize("Test text", Colors.BRIGHT_WHITE)
        if not test_text:
            logger.error("❌ Color function not working")
            return False
        
        logger.info("✅ Color display fix verified - all colors available")
        return True
        
    except Exception as e:
        logger.error(f"❌ Color display test failed: {e}")
        return False


def test_intelligent_data_handler():
    """Test the intelligent data handling system."""
    try:
        logger.info("🧠 Testing intelligent data handler...")
        
        # Initialize handler
        handler = IntelligentDataHandler()
        
        # Test with sample data
        import pandas as pd
        import numpy as np
        
        # Create sample data with missing values
        sample_data = pd.DataFrame({
            'home_team_id': [1, 2, 3, 4, 5],
            'away_team_id': [2, 3, 4, 5, 1],
            'league_id': [1, 1, 2, 2, 1],
            'home_goals': [2, 1, 3, 0, 2],
            'away_goals': [1, 1, 2, 1, 0],
            'home_shots': [12, np.nan, 15, np.nan, 10],
            'away_shots': [8, np.nan, 12, np.nan, 6],
            'home_possession': [55, np.nan, 60, np.nan, 50],
            'away_possession': [45, np.nan, 40, np.nan, 50],
            'home_corners': [6, np.nan, 8, np.nan, 4],
            'away_corners': [3, np.nan, 5, np.nan, 2]
        })
        
        logger.info(f"   📊 Sample data: {len(sample_data)} matches with missing values")
        
        # Test data availability analysis
        analysis = handler.analyze_data_availability(sample_data)
        if not analysis:
            logger.error("❌ Data availability analysis failed")
            return False
        
        logger.info(f"   ✅ Data availability analysis completed")
        logger.info(f"      • Total matches: {analysis.get('total_matches', 0)}")
        logger.info(f"      • Missing percentages calculated for {len(analysis.get('missing_percentages', {}))} columns")
        
        # Test team profiles
        team_profiles = handler.build_team_profiles(sample_data)
        if not team_profiles:
            logger.error("❌ Team profile building failed")
            return False
        
        logger.info(f"   ✅ Team profiles built for {len(team_profiles)} teams")
        
        # Test league profiles
        league_profiles = handler.build_league_profiles(sample_data)
        if not league_profiles:
            logger.error("❌ League profile building failed")
            return False
        
        logger.info(f"   ✅ League profiles built for {len(league_profiles)} leagues")
        
        # Test intelligent defaults
        defaults = handler.get_intelligent_defaults(1, 1, is_home=True)
        if not defaults or 'shots' not in defaults:
            logger.error("❌ Intelligent defaults generation failed")
            return False
        
        logger.info(f"   ✅ Intelligent defaults generated: {defaults}")
        
        # Test intelligent data filling
        filled_data = handler.fill_missing_data_intelligently(sample_data)
        if filled_data is None or len(filled_data) != len(sample_data):
            logger.error("❌ Intelligent data filling failed")
            return False
        
        # Check if missing values were filled
        original_nulls = sample_data.isnull().sum().sum()
        filled_nulls = filled_data.isnull().sum().sum()
        
        if filled_nulls >= original_nulls:
            logger.warning("⚠️ Data filling didn't reduce null values significantly")
        else:
            logger.info(f"   ✅ Data filling successful: {original_nulls} → {filled_nulls} null values")
        
        # Test alternative features
        enhanced_data = handler.create_alternative_features(sample_data)
        if enhanced_data is None or len(enhanced_data.columns) <= len(sample_data.columns):
            logger.error("❌ Alternative feature creation failed")
            return False
        
        new_features = len(enhanced_data.columns) - len(sample_data.columns)
        logger.info(f"   ✅ Alternative features created: {new_features} new features")
        
        logger.info("✅ Intelligent data handler test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Intelligent data handler test failed: {e}")
        import traceback
        logger.error(f"📋 Full traceback: {traceback.format_exc()}")
        return False


def test_model_integration():
    """Test that the model manager integrates correctly with intelligent data handling."""
    try:
        logger.info("🤖 Testing model integration with intelligent data handling...")
        
        # Initialize model manager
        model_manager = ModelManager()
        
        # Check that data handler is initialized
        if not hasattr(model_manager, 'data_handler'):
            logger.error("❌ Model manager missing data handler")
            return False
        
        if not isinstance(model_manager.data_handler, IntelligentDataHandler):
            logger.error("❌ Model manager data handler is wrong type")
            return False
        
        logger.info("✅ Model manager correctly integrated with intelligent data handler")
        return True
        
    except Exception as e:
        logger.error(f"❌ Model integration test failed: {e}")
        return False


def run_comprehensive_test():
    """Run comprehensive test of both fixes."""
    print("🔧 COMPREHENSIVE FIXES TEST")
    print("=" * 50)
    
    try:
        # Setup
        setup_logging()
        initialize_connection_pool()
        
        test_results = []
        
        # Test 1: Color display fix
        print("\n🎨 Test 1: Color Display Fix")
        print("-" * 30)
        if test_color_display_fix():
            print("✅ PASSED: Color display fix working")
            test_results.append(True)
        else:
            print("❌ FAILED: Color display fix not working")
            test_results.append(False)
        
        # Test 2: Intelligent data handler
        print("\n🧠 Test 2: Intelligent Data Handler")
        print("-" * 35)
        if test_intelligent_data_handler():
            print("✅ PASSED: Intelligent data handler working")
            test_results.append(True)
        else:
            print("❌ FAILED: Intelligent data handler not working")
            test_results.append(False)
        
        # Test 3: Model integration
        print("\n🤖 Test 3: Model Integration")
        print("-" * 25)
        if test_model_integration():
            print("✅ PASSED: Model integration working")
            test_results.append(True)
        else:
            print("❌ FAILED: Model integration not working")
            test_results.append(False)
        
        # Summary
        passed_tests = sum(test_results)
        total_tests = len(test_results)
        
        print(f"\n📊 TEST SUMMARY")
        print("=" * 20)
        print(f"Tests passed: {passed_tests}/{total_tests}")
        print(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ Color display error fixed")
            print("✅ Intelligent null data handling implemented")
            print("✅ Model integration working correctly")
            print("\n🚀 System ready for enhanced training and predictions!")
        else:
            print(f"\n⚠️ {total_tests - passed_tests} TEST(S) FAILED")
            print("Some issues need to be addressed before deployment")
        
        return passed_tests == total_tests
        
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        close_connection_pool()


def main():
    """Main function."""
    print("🔧 FIXES VERIFICATION TEST")
    print("=" * 30)
    print()
    print("This script tests both fixes:")
    print("1. 🎨 Color display error in predictions viewer")
    print("2. 🧠 Intelligent null data handling for model training")
    print()
    
    if run_comprehensive_test():
        print("\n✅ All fixes verified and working correctly!")
        return True
    else:
        print("\n❌ Some fixes need attention!")
        return False


if __name__ == "__main__":
    main()
