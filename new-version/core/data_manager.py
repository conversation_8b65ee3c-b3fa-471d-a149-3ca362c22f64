"""
Data management for fetching and storing football data.
"""
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, date, timedelta

from api import FootballApiClient
from database import get_db_cursor
from database.models import Team, League, Match, UpcomingGame, MatchStatus
from storage import MatchStorage, TeamStorage, LeagueStorage

logger = logging.getLogger(__name__)


class DataManager:
    """Manages data fetching and storage operations."""

    def __init__(self):
        """Initialize data manager."""
        self.api_client = FootballApiClient()
        self.match_storage = MatchStorage()
        self.team_storage = TeamStorage()
        self.league_storage = LeagueStorage()

    def test_api_connection(self) -> bool:
        """
        Test API connection.

        Returns:
            True if connection successful, False otherwise
        """
        return self.api_client.test_connection()

    def fetch_and_store_leagues(self, country: str, season: int) -> int:
        """
        Fetch leagues for a country and season, store in database.

        Args:
            country: Country name
            season: Season year

        Returns:
            Number of leagues stored
        """
        try:
            logger.info(f"Fetching leagues for {country}, season {season}")

            # Fetch from API
            leagues_data = self.api_client.get_leagues(country=country, season=season)

            if not leagues_data:
                logger.warning(f"No leagues found for {country}, season {season}")
                return 0

            # Convert to League objects and store
            leagues = []
            for league_data in leagues_data:
                league_info = league_data.get('league', {})
                seasons_info = league_data.get('seasons', [])

                # Find the specific season
                season_info = None
                for s in seasons_info:
                    if s.get('year') == season:
                        season_info = s
                        break

                if not season_info:
                    continue

                league = League(
                    id=league_info.get('id'),
                    name=league_info.get('name'),
                    country=league_data.get('country', {}).get('name', country),
                    season=season,
                    logo=league_info.get('logo'),
                    flag=league_data.get('country', {}).get('flag'),
                    start_date=self._parse_date(season_info.get('start')),
                    end_date=self._parse_date(season_info.get('end')),
                    current=season_info.get('current', False)
                )
                leagues.append(league)

            # Store in database
            stored_count = self.league_storage.store_leagues(leagues)
            logger.info(f"Stored {stored_count} leagues for {country}, season {season}")

            return stored_count

        except Exception as e:
            logger.error(f"Failed to fetch and store leagues: {e}")
            return 0

    def fetch_and_store_leagues_by_id(self, league_id: int, season: int, country: str = None) -> int:
        """
        Fetch a specific league by ID and season, store in database.

        Args:
            league_id: League ID
            season: Season year
            country: Country name (optional)

        Returns:
            Number of leagues stored (0 or 1)
        """
        try:
            logger.info(f"Fetching league {league_id} for season {season}")

            # Fetch from API
            leagues_data = self.api_client.get_leagues(league_id=league_id, season=season)

            if not leagues_data:
                logger.warning(f"League {league_id} not found for season {season}")
                return 0

            # Take the first (should be only) result
            league_data = leagues_data[0]
            league_info = league_data.get('league', {})
            seasons_info = league_data.get('seasons', [])

            # Find the specific season
            season_info = None
            for s in seasons_info:
                if s.get('year') == season:
                    season_info = s
                    break

            if not season_info:
                logger.warning(f"Season {season} not found for league {league_id}")
                return 0

            league = League(
                id=league_info.get('id'),
                name=league_info.get('name'),
                country=league_data.get('country', {}).get('name', country or 'Unknown'),
                season=season,
                logo=league_info.get('logo'),
                flag=league_data.get('country', {}).get('flag'),
                start_date=self._parse_date(season_info.get('start')),
                end_date=self._parse_date(season_info.get('end')),
                current=season_info.get('current', False)
            )

            # Store in database
            stored_count = self.league_storage.store_leagues([league])
            logger.info(f"Stored league {league_info.get('name')} for season {season}")

            return stored_count

        except Exception as e:
            logger.error(f"Failed to fetch and store league by ID: {e}")
            return 0

    def fetch_and_store_teams(self, league_id: int, season: int) -> int:
        """
        Fetch teams for a league and season, store in database.

        Args:
            league_id: League ID
            season: Season year

        Returns:
            Number of teams stored
        """
        try:
            logger.info(f"Fetching teams for league {league_id}, season {season}")

            # Fetch from API
            teams_data = self.api_client.get_teams(league_id, season)

            if not teams_data:
                logger.warning(f"No teams found for league {league_id}, season {season}")
                return 0

            # Convert to Team objects and store
            teams = []
            for team_data in teams_data:
                team_info = team_data.get('team', {})
                venue_info = team_data.get('venue', {})

                team = Team(
                    id=team_info.get('id'),
                    name=team_info.get('name'),
                    code=team_info.get('code'),
                    country=team_info.get('country'),
                    founded=team_info.get('founded'),
                    logo=team_info.get('logo'),
                    venue_id=venue_info.get('id'),
                    venue_name=venue_info.get('name'),
                    venue_capacity=venue_info.get('capacity')
                )
                teams.append(team)

            # Store in database
            stored_count = self.team_storage.store_teams(teams)
            logger.info(f"Stored {stored_count} teams for league {league_id}, season {season}")

            return stored_count

        except Exception as e:
            logger.error(f"Failed to fetch and store teams: {e}")
            return 0

    def fetch_and_store_matches(
        self,
        league_id: int,
        season: int,
        date_from: Optional[date] = None,
        date_to: Optional[date] = None
    ) -> Dict[str, int]:
        """
        Fetch matches for a league and season, store in appropriate tables.

        Args:
            league_id: League ID
            season: Season year
            date_from: Start date filter
            date_to: End date filter

        Returns:
            Dictionary with counts of stored matches and upcoming games
        """
        try:
            import time

            date_range_str = ""
            if date_from or date_to:
                date_range_str = f" (from {date_from or 'start'} to {date_to or 'end'})"

            logger.info(f"🔄 Fetching matches for league {league_id}, season {season}{date_range_str}")
            print(f"  🏆 Fetching fixtures from API...")

            # Fetch from API with timing
            fetch_start = time.time()
            fixtures_data = self.api_client.get_fixtures(
                league_id=league_id,
                season=season,
                date_from=date_from,
                date_to=date_to
            )
            fetch_time = time.time() - fetch_start

            if not fixtures_data:
                logger.warning(f"No fixtures found for league {league_id}, season {season}")
                print(f"  ⚠️  No fixtures found for this league and season")
                return {'matches': 0, 'upcoming_games': 0}

            logger.info(f"📥 Fetched {len(fixtures_data)} fixtures in {fetch_time:.2f}s")
            print(f"  ✅ Fetched {len(fixtures_data)} fixtures from API in {fetch_time:.1f}s")

            # Separate completed and upcoming matches
            completed_matches = []
            upcoming_games = []

            print(f"  🔄 Processing {len(fixtures_data)} fixtures...")

            for fixture_data in fixtures_data:
                fixture_info = fixture_data.get('fixture', {})
                league_info = fixture_data.get('league', {})
                teams_info = fixture_data.get('teams', {})
                goals_info = fixture_data.get('goals', {})
                score_info = fixture_data.get('score', {})

                # Parse match data
                match_id = fixture_info.get('id')
                match_date = self._parse_datetime(fixture_info.get('date'))
                status = MatchStatus(fixture_info.get('status', {}).get('short', 'NS'))

                home_team_id = teams_info.get('home', {}).get('id')
                away_team_id = teams_info.get('away', {}).get('id')

                home_goals = goals_info.get('home')
                away_goals = goals_info.get('away')

                # Determine if match is completed
                is_finished = status in [MatchStatus.FINISHED, MatchStatus.AFTER_EXTRA_TIME, MatchStatus.AFTER_PENALTIES]

                if is_finished and home_goals is not None and away_goals is not None:
                    # Completed match
                    match = Match(
                        id=match_id,
                        league_id=league_id,
                        season=season,
                        round=league_info.get('round', ''),
                        date=match_date,
                        home_team_id=home_team_id,
                        away_team_id=away_team_id,
                        home_goals=home_goals,
                        away_goals=away_goals,
                        status=status,
                        venue_id=fixture_info.get('venue', {}).get('id'),
                        referee=fixture_info.get('referee')
                    )
                    completed_matches.append(match)
                else:
                    # Upcoming game
                    game = UpcomingGame(
                        id=match_id,
                        league_id=league_id,
                        season=season,
                        round=league_info.get('round', ''),
                        date=match_date,
                        home_team_id=home_team_id,
                        away_team_id=away_team_id,
                        status=status,
                        venue_id=fixture_info.get('venue', {}).get('id'),
                        referee=fixture_info.get('referee'),
                        home_goals=home_goals,
                        away_goals=away_goals
                    )
                    upcoming_games.append(game)

            # Store in database with timing
            store_start = time.time()
            print(f"  💾 Storing {len(completed_matches)} completed matches and {len(upcoming_games)} upcoming games...")

            matches_count = self.match_storage.store_matches(completed_matches)
            upcoming_count = self.match_storage.store_upcoming_games(upcoming_games)
            store_time = time.time() - store_start

            logger.info(f"💾 Stored {matches_count} completed matches and {upcoming_count} upcoming games in {store_time:.2f}s")
            print(f"  ✅ Stored {matches_count} completed matches and {upcoming_count} upcoming games")

            # Fetch and store statistics for completed matches (only if needed)
            if completed_matches:
                self._fetch_and_store_match_statistics_optimized(completed_matches)

            return {'matches': matches_count, 'upcoming_games': upcoming_count}

        except Exception as e:
            logger.error(f"Failed to fetch and store matches: {e}")
            return {'matches': 0, 'upcoming_games': 0}

    def update_match_results(self) -> int:
        """
        Update results for matches that may have been completed.

        Returns:
            Number of matches updated
        """
        try:
            logger.info("Updating match results from API")

            # Get upcoming games that might be finished
            with get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT id FROM upcoming_games
                    WHERE date < NOW() - INTERVAL '2 hours'
                    AND status NOT IN ('FT', 'AET', 'PEN')
                    ORDER BY date DESC
                    LIMIT 50
                """)
                match_ids = [row['id'] for row in cursor.fetchall()]

            if not match_ids:
                logger.info("No matches to update")
                return 0

            updated_count = 0

            # Update each match
            for match_id in match_ids:
                try:
                    fixtures_data = self.api_client.get_fixtures(fixture_id=match_id)

                    if fixtures_data:
                        fixture_data = fixtures_data[0]
                        fixture_info = fixture_data.get('fixture', {})
                        goals_info = fixture_data.get('goals', {})

                        status = MatchStatus(fixture_info.get('status', {}).get('short', 'NS'))
                        home_goals = goals_info.get('home')
                        away_goals = goals_info.get('away')

                        # Update in database
                        with get_db_cursor() as cursor:
                            cursor.execute("""
                                UPDATE upcoming_games
                                SET status = %s, home_goals = %s, away_goals = %s, updated_at = NOW()
                                WHERE id = %s
                            """, (status.value, home_goals, away_goals, match_id))

                            if cursor.rowcount > 0:
                                updated_count += 1

                except Exception as e:
                    logger.error(f"Failed to update match {match_id}: {e}")
                    continue

            logger.info(f"Updated {updated_count} matches")
            return updated_count

        except Exception as e:
            logger.error(f"Failed to update match results: {e}")
            return 0

    def move_completed_games_to_matches(self) -> int:
        """
        Move completed games from upcoming_games to matches table.

        Returns:
            Number of games moved
        """
        return self.match_storage.move_completed_games_to_matches()

    def _parse_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse date string to datetime object."""
        if not date_str:
            return None
        try:
            return datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            return None

    def _parse_datetime(self, datetime_str: Optional[str]) -> Optional[datetime]:
        """Parse datetime string to datetime object."""
        if not datetime_str:
            return None
        try:
            # Handle ISO format with timezone
            if 'T' in datetime_str:
                return datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return None

    def _fetch_and_store_match_statistics(self, matches: List[Match]) -> None:
        """
        Fetch and store detailed statistics for completed matches.

        Args:
            matches: List of completed matches to fetch statistics for
        """
        import time

        total_matches = len(matches)
        logger.info(f"🔄 Fetching statistics for {total_matches} completed matches...")
        print(f"  📊 Processing {total_matches} matches for detailed statistics...")

        successful_updates = 0
        failed_updates = 0
        no_stats_available = 0
        start_time = time.time()

        for i, match in enumerate(matches, 1):
            try:
                # Live progress logging
                if i % 5 == 0 or i == total_matches:
                    elapsed = time.time() - start_time
                    avg_time = elapsed / i if i > 0 else 0
                    remaining = (total_matches - i) * avg_time
                    print(f"  📈 Progress: {i}/{total_matches} matches processed "
                          f"({i/total_matches*100:.1f}%) - "
                          f"ETA: {remaining:.1f}s")

                logger.debug(f"Fetching statistics for match {match.id} ({i}/{total_matches})")

                # Fetch statistics for both teams with timing
                stats_start = time.time()
                home_stats = self.api_client.get_fixture_statistics(match.id, match.home_team_id)
                away_stats = self.api_client.get_fixture_statistics(match.id, match.away_team_id)
                stats_time = time.time() - stats_start

                if home_stats and away_stats:
                    # Parse statistics
                    home_statistics = self._parse_team_statistics(home_stats)
                    away_statistics = self._parse_team_statistics(away_stats)

                    # Update match with statistics
                    self._update_match_statistics(match.id, home_statistics, away_statistics)

                    successful_updates += 1
                    logger.debug(f"✅ Updated statistics for match {match.id} in {stats_time:.2f}s")
                else:
                    no_stats_available += 1
                    logger.warning(f"⚠️  No statistics available for match {match.id}")

                # Rate limiting - respect API limits
                time.sleep(0.5)  # 500ms between requests for better performance

            except Exception as e:
                failed_updates += 1
                logger.error(f"❌ Failed to fetch statistics for match {match.id}: {e}")
                continue

        total_time = time.time() - start_time
        avg_time_per_match = total_time / total_matches if total_matches > 0 else 0

        # Final summary
        logger.info(f"📊 Statistics fetching completed in {total_time:.2f}s:")
        logger.info(f"  ✅ Successfully updated: {successful_updates}")
        logger.info(f"  ⚠️  No stats available: {no_stats_available}")
        logger.info(f"  ❌ Failed: {failed_updates}")
        logger.info(f"  ⏱️  Average time per match: {avg_time_per_match:.2f}s")

        print(f"  ✅ Statistics update completed!")
        print(f"     • {successful_updates} matches updated with statistics")
        print(f"     • {no_stats_available} matches had no statistics available")
        print(f"     • {failed_updates} matches failed to update")
        print(f"     • Total time: {total_time:.1f}s")

    def _fetch_and_store_match_statistics_optimized(self, matches: List[Match]) -> None:
        """
        Optimized version: Only fetch statistics for matches that don't have them yet.

        Args:
            matches: List of completed matches to check and fetch statistics for
        """
        import time
        from concurrent.futures import ThreadPoolExecutor, as_completed

        # First, check which matches already have statistics
        match_ids = [match.id for match in matches]

        if not match_ids:
            return

        print(f"  🔍 Checking which of {len(matches)} matches need statistics...")

        # Check database for matches without statistics
        with get_db_cursor() as cursor:
            placeholders = ','.join(['%s'] * len(match_ids))
            cursor.execute(f"""
                SELECT id, home_team_id, away_team_id
                FROM matches
                WHERE id IN ({placeholders})
                AND (home_shots IS NULL OR home_fouls IS NULL OR home_yellow_cards IS NULL)
                ORDER BY date DESC
            """, match_ids)
            matches_needing_stats = cursor.fetchall()

        if not matches_needing_stats:
            logger.info("✅ All matches already have statistics!")
            print(f"  ✅ All matches already have complete statistics - skipping!")
            return

        total_matches = len(matches_needing_stats)
        skipped_matches = len(matches) - total_matches

        logger.info(f"🔄 Fetching statistics for {total_matches} matches (skipped {skipped_matches} with existing stats)")
        print(f"  📊 Processing {total_matches} matches needing statistics (skipped {skipped_matches})...")

        successful_updates = 0
        failed_updates = 0
        no_stats_available = 0
        start_time = time.time()

        # Use ThreadPoolExecutor for parallel API calls (balanced for stability)
        max_workers = 3  # Reduced to respect API rate limits better

        def fetch_match_stats(match_data):
            """Fetch statistics for a single match."""
            match_id = match_data['id']
            home_team_id = match_data['home_team_id']
            away_team_id = match_data['away_team_id']

            try:
                # Fetch statistics for both teams
                home_stats = self.api_client.get_fixture_statistics(match_id, home_team_id)
                away_stats = self.api_client.get_fixture_statistics(match_id, away_team_id)

                # Debug logging for problematic matches
                if not home_stats or not away_stats:
                    logger.debug(f"🔍 Debug match {match_id}: home_stats={len(home_stats) if home_stats else 0} items, away_stats={len(away_stats) if away_stats else 0} items")

                    # Check if it's an empty response vs no response
                    if home_stats is not None and away_stats is not None:
                        if len(home_stats) == 0 or len(away_stats) == 0:
                            logger.debug(f"🔍 Match {match_id}: Empty statistics response from API")
                        else:
                            logger.debug(f"🔍 Match {match_id}: Statistics response structure issue")
                            logger.debug(f"   Home stats sample: {home_stats[:1] if home_stats else 'None'}")
                            logger.debug(f"   Away stats sample: {away_stats[:1] if away_stats else 'None'}")
                    else:
                        logger.debug(f"🔍 Match {match_id}: No response from API (home_stats={home_stats}, away_stats={away_stats})")

                if home_stats and away_stats:
                    # Parse statistics
                    home_statistics = self._parse_team_statistics(home_stats)
                    away_statistics = self._parse_team_statistics(away_stats)

                    return {
                        'match_id': match_id,
                        'home_stats': home_statistics,
                        'away_stats': away_statistics,
                        'success': True
                    }
                else:
                    return {
                        'match_id': match_id,
                        'success': False,
                        'reason': 'no_stats',
                        'debug_info': {
                            'home_stats_count': len(home_stats) if home_stats else 0,
                            'away_stats_count': len(away_stats) if away_stats else 0,
                            'home_stats_exists': home_stats is not None,
                            'away_stats_exists': away_stats is not None
                        }
                    }

            except Exception as e:
                return {
                    'match_id': match_id,
                    'success': False,
                    'reason': 'error',
                    'error': str(e)
                }

        # Process matches in parallel with progress tracking
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_match = {
                executor.submit(fetch_match_stats, match_data): match_data
                for match_data in matches_needing_stats
            }

            # Collect results with progress tracking
            completed_count = 0
            batch_updates = []

            for future in as_completed(future_to_match):
                completed_count += 1

                # Progress logging every 25 matches or at the end
                if completed_count % 25 == 0 or completed_count == total_matches:
                    elapsed = time.time() - start_time
                    avg_time = elapsed / completed_count if completed_count > 0 else 0
                    remaining = (total_matches - completed_count) * avg_time
                    print(f"  📈 Progress: {completed_count}/{total_matches} matches processed "
                          f"({completed_count/total_matches*100:.1f}%) - "
                          f"ETA: {remaining:.1f}s")

                try:
                    result = future.result()

                    if result['success']:
                        # Add to batch for database update
                        batch_updates.append(result)
                        successful_updates += 1

                        # Process batch updates every 40 matches for better performance
                        if len(batch_updates) >= 40:
                            self._batch_update_match_statistics(batch_updates)
                            batch_updates = []

                    elif result['reason'] == 'no_stats':
                        no_stats_available += 1
                        debug_info = result.get('debug_info', {})
                        logger.warning(f"⚠️  No statistics available for match {result['match_id']} "
                                     f"(home: {debug_info.get('home_stats_count', 0)} items, "
                                     f"away: {debug_info.get('away_stats_count', 0)} items)")

                        # Check if this is a match that should have statistics
                        self._investigate_missing_stats(result['match_id'])
                    else:
                        failed_updates += 1
                        logger.error(f"❌ Failed to fetch statistics for match {result['match_id']}: {result.get('error', 'Unknown error')}")

                except Exception as e:
                    failed_updates += 1
                    logger.error(f"❌ Error processing future result: {e}")

            # Process remaining batch updates
            if batch_updates:
                self._batch_update_match_statistics(batch_updates)

        total_time = time.time() - start_time
        avg_time_per_match = total_time / total_matches if total_matches > 0 else 0

        # Final summary
        logger.info(f"📊 Optimized statistics fetching completed in {total_time:.2f}s:")
        logger.info(f"  ✅ Successfully updated: {successful_updates}")
        logger.info(f"  ⚠️  No stats available: {no_stats_available}")
        logger.info(f"  ❌ Failed: {failed_updates}")
        logger.info(f"  ⏭️  Skipped (already had stats): {skipped_matches}")
        logger.info(f"  ⏱️  Average time per match: {avg_time_per_match:.2f}s")

        print(f"  ✅ Statistics update completed!")
        print(f"     • {successful_updates} matches updated with statistics")
        print(f"     • {no_stats_available} matches had no statistics available")
        print(f"     • {failed_updates} matches failed to update")
        print(f"     • {skipped_matches} matches skipped (already had stats)")
        print(f"     • Total time: {total_time:.1f}s (avg: {avg_time_per_match:.2f}s per match)")

        # Retry failed matches with slower, more careful approach
        if no_stats_available > 0:
            print(f"\n🔄 Retrying {no_stats_available} matches with slower approach...")
            retry_count = self._retry_missing_statistics_slow()
            if retry_count > 0:
                print(f"  ✅ Successfully recovered {retry_count} matches on retry!")
            else:
                print(f"  ⚠️  No additional matches recovered on retry")

    def _batch_update_match_statistics(self, batch_updates: List[Dict]) -> None:
        """
        Update multiple matches with statistics in a single database transaction.

        Args:
            batch_updates: List of match statistics to update
        """
        if not batch_updates:
            return

        try:
            with get_db_cursor() as cursor:
                # Prepare batch update query
                update_data = []
                for update in batch_updates:
                    match_id = update['match_id']
                    home_stats = update['home_stats']
                    away_stats = update['away_stats']

                    update_data.append((
                        home_stats.get('shots'),
                        away_stats.get('shots'),
                        home_stats.get('shots_on_target'),
                        away_stats.get('shots_on_target'),
                        home_stats.get('possession'),
                        away_stats.get('possession'),
                        home_stats.get('corners'),
                        away_stats.get('corners'),
                        home_stats.get('fouls'),
                        away_stats.get('fouls'),
                        home_stats.get('yellow_cards'),
                        away_stats.get('yellow_cards'),
                        home_stats.get('red_cards'),
                        away_stats.get('red_cards'),
                        match_id
                    ))

                # Execute batch update
                cursor.executemany("""
                    UPDATE matches SET
                        home_shots = %s,
                        away_shots = %s,
                        home_shots_on_target = %s,
                        away_shots_on_target = %s,
                        home_possession = %s,
                        away_possession = %s,
                        home_corners = %s,
                        away_corners = %s,
                        home_fouls = %s,
                        away_fouls = %s,
                        home_yellow_cards = %s,
                        away_yellow_cards = %s,
                        home_red_cards = %s,
                        away_red_cards = %s,
                        updated_at = NOW()
                    WHERE id = %s
                """, update_data)

                logger.debug(f"Batch updated {len(batch_updates)} matches with statistics")

        except Exception as e:
            logger.error(f"Failed to batch update match statistics: {e}")
            # Fallback to individual updates
            for update in batch_updates:
                try:
                    self._update_match_statistics(update['match_id'], update['home_stats'], update['away_stats'])
                except Exception as individual_error:
                    logger.error(f"Failed individual update for match {update['match_id']}: {individual_error}")

    def _investigate_missing_stats(self, match_id: int) -> None:
        """
        Investigate why a match has no statistics available.

        Args:
            match_id: Match ID to investigate
        """
        try:
            # Get match details from database
            with get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT date, status, home_goals, away_goals, round
                    FROM matches
                    WHERE id = %s
                """, (match_id,))
                match_info = cursor.fetchone()

            if not match_info:
                logger.debug(f"🔍 Match {match_id}: Not found in database")
                return

            match_date = match_info['date']
            match_status = match_info['status']
            home_goals = match_info['home_goals']
            away_goals = match_info['away_goals']
            round_info = match_info['round']

            # Analyze why statistics might be missing
            reasons = []

            # Check if match is too old (statistics might not be available for very old matches)
            if match_date:
                from datetime import datetime, timedelta
                if match_date < datetime.now() - timedelta(days=365 * 3):  # 3 years old
                    reasons.append("match is older than 3 years")

            # Check match status
            if match_status not in ['FT', 'AET', 'PEN']:
                reasons.append(f"match status is '{match_status}' (not finished)")

            # Check if it's a walkover or forfeit (0-0 or unusual scores)
            if home_goals == 0 and away_goals == 0:
                reasons.append("possible walkover/forfeit (0-0 score)")

            # Check if it's a qualification round or special match
            if round_info and any(keyword in round_info.lower() for keyword in ['qualification', 'playoff', 'preliminary']):
                reasons.append(f"special round type: {round_info}")

            if reasons:
                logger.debug(f"🔍 Match {match_id} missing stats - possible reasons: {', '.join(reasons)}")
            else:
                logger.debug(f"🔍 Match {match_id} missing stats - no obvious reason (date: {match_date}, status: {match_status}, score: {home_goals}-{away_goals})")

                # For recent finished matches with no obvious reason, this might be an API issue
                if match_date and match_date > datetime.now() - timedelta(days=30) and match_status == 'FT':
                    logger.warning(f"🚨 Recent finished match {match_id} has no statistics - possible API issue")

        except Exception as e:
            logger.debug(f"🔍 Error investigating match {match_id}: {e}")

    def _retry_missing_statistics_slow(self) -> int:
        """
        Retry fetching statistics for matches that failed in the fast parallel approach.
        Uses slower, more careful API calls with longer delays.

        Returns:
            Number of matches successfully recovered
        """
        import time

        try:
            # Find matches that still don't have statistics after the fast approach
            with get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT id, home_team_id, away_team_id, date, status
                    FROM matches
                    WHERE (home_shots IS NULL OR home_fouls IS NULL OR home_yellow_cards IS NULL)
                    AND status IN ('FT', 'AET', 'PEN')
                    AND date > NOW() - INTERVAL '30 days'
                    ORDER BY date DESC
                    LIMIT 50
                """)
                retry_matches = cursor.fetchall()

            if not retry_matches:
                logger.info("No recent matches need retry")
                return 0

            logger.info(f"🔄 Retrying {len(retry_matches)} recent matches with slow approach")

            successful_retries = 0

            for i, match_data in enumerate(retry_matches, 1):
                match_id = match_data['id']
                home_team_id = match_data['home_team_id']
                away_team_id = match_data['away_team_id']

                try:
                    logger.debug(f"🔄 Retry {i}/{len(retry_matches)}: Match {match_id}")

                    # Wait longer between requests (2 seconds instead of 0.2)
                    time.sleep(2.0)

                    # Try different approaches
                    success = False

                    # Approach 1: Try with team parameters (original approach)
                    home_stats = self.api_client.get_fixture_statistics(match_id, home_team_id)
                    time.sleep(1.0)  # Extra delay between home and away
                    away_stats = self.api_client.get_fixture_statistics(match_id, away_team_id)

                    if home_stats and away_stats:
                        logger.debug(f"✅ Retry success with team parameters for match {match_id}")
                        success = True
                    else:
                        # Approach 2: Try without team parameters and parse both teams from response
                        logger.debug(f"🔄 Trying without team parameters for match {match_id}")
                        time.sleep(1.0)
                        all_stats = self.api_client.get_fixture_statistics(match_id)

                        if all_stats and len(all_stats) >= 2:
                            # Try to identify home and away team stats
                            home_stats = None
                            away_stats = None

                            for stat_entry in all_stats:
                                team_info = stat_entry.get('team', {})
                                team_id = team_info.get('id')

                                if team_id == home_team_id:
                                    home_stats = [stat_entry]
                                elif team_id == away_team_id:
                                    away_stats = [stat_entry]

                            if home_stats and away_stats:
                                logger.debug(f"✅ Retry success without team parameters for match {match_id}")
                                success = True

                    if success and home_stats and away_stats:
                        # Parse and update statistics
                        home_statistics = self._parse_team_statistics(home_stats)
                        away_statistics = self._parse_team_statistics(away_stats)

                        self._update_match_statistics(match_id, home_statistics, away_statistics)
                        successful_retries += 1

                        logger.info(f"✅ Retry successful for match {match_id}")
                    else:
                        logger.debug(f"⚠️  Retry failed for match {match_id} - still no statistics")

                        # Log detailed debug info for persistent failures
                        logger.debug(f"   Home stats: {len(home_stats) if home_stats else 0} items")
                        logger.debug(f"   Away stats: {len(away_stats) if away_stats else 0} items")
                        if home_stats:
                            logger.debug(f"   Home sample: {home_stats[0] if home_stats else 'None'}")
                        if away_stats:
                            logger.debug(f"   Away sample: {away_stats[0] if away_stats else 'None'}")

                except Exception as e:
                    logger.error(f"❌ Error in retry for match {match_id}: {e}")
                    continue

            logger.info(f"🔄 Retry completed: {successful_retries}/{len(retry_matches)} matches recovered")
            return successful_retries

        except Exception as e:
            logger.error(f"Error in retry missing statistics: {e}")
            return 0

    def _parse_team_statistics(self, stats_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Parse team statistics from API response.

        Args:
            stats_data: Statistics data from API

        Returns:
            Dictionary with parsed statistics
        """
        # Initialize with default values (0 for cards/fouls, None for others)
        stats = {
            'shots': None,
            'shots_on_target': None,
            'possession': None,
            'corners': None,
            'fouls': 0,  # Default to 0 for fouls
            'yellow_cards': 0,  # Default to 0 for yellow cards
            'red_cards': 0  # Default to 0 for red cards
        }

        if not stats_data:
            return stats

        # Get the statistics array from the first response item
        statistics = stats_data[0].get('statistics', [])

        # Map API statistics to our database fields
        stat_mapping = {
            'Total Shots': 'shots',
            'Shots on Goal': 'shots_on_target',
            'Ball Possession': 'possession',
            'Corner Kicks': 'corners',
            'Fouls': 'fouls',
            'Yellow Cards': 'yellow_cards',
            'Red Cards': 'red_cards'
        }

        # Fields that should default to 0 if no data found
        zero_default_fields = {'fouls', 'yellow_cards', 'red_cards'}

        for stat in statistics:
            stat_type = stat.get('type')
            stat_value = stat.get('value')

            if stat_type in stat_mapping:
                field_name = stat_mapping[stat_type]

                # Handle percentage values (like possession)
                if stat_type == 'Ball Possession' and isinstance(stat_value, str):
                    try:
                        # Remove % sign and convert to float
                        stats[field_name] = float(stat_value.replace('%', '')) / 100.0
                    except (ValueError, AttributeError):
                        stats[field_name] = None
                elif stat_value is not None and stat_value != '':
                    # Convert to integer for count statistics
                    try:
                        stats[field_name] = int(stat_value)
                    except (ValueError, TypeError):
                        # If conversion fails, use 0 for cards/fouls, None for others
                        stats[field_name] = 0 if field_name in zero_default_fields else None
                # If stat_value is None or empty, keep the default value (already set above)

        return stats

    def _update_match_statistics(self, match_id: int, home_stats: Dict[str, Any], away_stats: Dict[str, Any]) -> None:
        """
        Update match with detailed statistics.

        Args:
            match_id: Match ID
            home_stats: Home team statistics
            away_stats: Away team statistics
        """
        try:
            with get_db_cursor() as cursor:
                cursor.execute("""
                    UPDATE matches SET
                        home_shots = %s,
                        away_shots = %s,
                        home_shots_on_target = %s,
                        away_shots_on_target = %s,
                        home_possession = %s,
                        away_possession = %s,
                        home_corners = %s,
                        away_corners = %s,
                        home_fouls = %s,
                        away_fouls = %s,
                        home_yellow_cards = %s,
                        away_yellow_cards = %s,
                        home_red_cards = %s,
                        away_red_cards = %s,
                        updated_at = NOW()
                    WHERE id = %s
                """, (
                    home_stats.get('shots'),
                    away_stats.get('shots'),
                    home_stats.get('shots_on_target'),
                    away_stats.get('shots_on_target'),
                    home_stats.get('possession'),
                    away_stats.get('possession'),
                    home_stats.get('corners'),
                    away_stats.get('corners'),
                    home_stats.get('fouls'),
                    away_stats.get('fouls'),
                    home_stats.get('yellow_cards'),
                    away_stats.get('yellow_cards'),
                    home_stats.get('red_cards'),
                    away_stats.get('red_cards'),
                    match_id
                ))

                if cursor.rowcount > 0:
                    logger.debug(f"Updated statistics for match {match_id}")
                else:
                    logger.warning(f"No match found with ID {match_id} to update statistics")

        except Exception as e:
            logger.error(f"Failed to update statistics for match {match_id}: {e}")
            raise