#!/usr/bin/env python3
"""
Fix the models table constraint to allow multiple versions of the same model.
"""
import sys
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database.connection import initialize_connection_pool, get_db_cursor, close_connection_pool
from utils.logger import setup_logging

logger = logging.getLogger(__name__)


def fix_models_table_constraint():
    """Fix the models table constraint to allow model versioning."""
    try:
        logger.info("🔧 Fixing models table constraint...")
        
        with get_db_cursor() as cursor:
            # Check if the old constraint exists
            cursor.execute("""
                SELECT constraint_name 
                FROM information_schema.table_constraints 
                WHERE table_name = 'models' 
                AND constraint_type = 'UNIQUE'
                AND constraint_name = 'models_name_key'
            """)
            
            old_constraint = cursor.fetchone()
            
            if old_constraint:
                logger.info("   📋 Found old unique constraint on 'name' column")
                
                # Drop the old constraint
                logger.info("   🗑️ Dropping old unique constraint...")
                cursor.execute("ALTER TABLE models DROP CONSTRAINT models_name_key")
                logger.info("   ✅ Old constraint dropped")
                
                # Add new composite unique constraint
                logger.info("   🔧 Adding new composite unique constraint...")
                cursor.execute("ALTER TABLE models ADD CONSTRAINT models_name_version_key UNIQUE (name, version)")
                logger.info("   ✅ New constraint added: UNIQUE(name, version)")
                
                # Add index for better performance
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_models_name_version ON models(name, version)")
                logger.info("   📊 Added performance index")
                
            else:
                logger.info("   ✅ No old constraint found - table already has correct schema")
        
        logger.info("✅ Models table constraint fixed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to fix models table constraint: {e}")
        import traceback
        logger.error(f"📋 Full traceback: {traceback.format_exc()}")
        return False


def verify_constraint_fix():
    """Verify that the constraint fix worked correctly."""
    try:
        logger.info("🔍 Verifying constraint fix...")
        
        with get_db_cursor() as cursor:
            # Check current constraints
            cursor.execute("""
                SELECT constraint_name, constraint_type
                FROM information_schema.table_constraints 
                WHERE table_name = 'models' 
                AND constraint_type = 'UNIQUE'
            """)
            
            constraints = cursor.fetchall()
            
            logger.info("   📋 Current unique constraints:")
            for constraint in constraints:
                logger.info(f"      • {constraint['constraint_name']}: {constraint['constraint_type']}")
            
            # Check if we can insert multiple models with same name but different versions
            cursor.execute("SELECT COUNT(*) as count FROM models WHERE name = 'test_model'")
            existing_count = cursor.fetchone()['count']
            
            if existing_count == 0:
                # Test inserting two models with same name but different versions
                logger.info("   🧪 Testing constraint with sample data...")
                
                cursor.execute("""
                    INSERT INTO models (name, version, model_type, model_data, feature_columns, training_samples)
                    VALUES ('test_model', 'v1', 'test', %s, '[]', 100)
                """, (b'test_data',))
                
                cursor.execute("""
                    INSERT INTO models (name, version, model_type, model_data, feature_columns, training_samples)
                    VALUES ('test_model', 'v2', 'test', %s, '[]', 100)
                """, (b'test_data',))
                
                # Clean up test data
                cursor.execute("DELETE FROM models WHERE name = 'test_model'")
                
                logger.info("   ✅ Constraint test passed - multiple versions allowed")
            else:
                logger.info("   ✅ Constraint appears to be working (existing test data found)")
        
        logger.info("✅ Constraint verification completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Constraint verification failed: {e}")
        return False


def main():
    """Main function to fix the models table constraint."""
    print("🔧 Models Table Constraint Fix")
    print("=" * 50)
    
    try:
        # Setup logging
        setup_logging()
        
        # Initialize database connection
        initialize_connection_pool()
        
        # Fix the constraint
        if fix_models_table_constraint():
            # Verify the fix
            if verify_constraint_fix():
                print("\n✅ SUCCESS: Models table constraint fixed!")
                print("\n📋 What was fixed:")
                print("   • Removed UNIQUE constraint on 'name' column")
                print("   • Added UNIQUE constraint on (name, version) combination")
                print("   • Added performance index for name+version lookups")
                print("\n🎯 Result:")
                print("   • Multiple model versions with same name are now allowed")
                print("   • Each (name, version) combination must still be unique")
                print("   • Model versioning system will work correctly")
                print("\n🚀 You can now retrain your model successfully!")
            else:
                print("\n⚠️ WARNING: Constraint was fixed but verification failed")
        else:
            print("\n❌ FAILED: Could not fix models table constraint")
            print("   Check the logs for detailed error information")
        
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        close_connection_pool()


if __name__ == "__main__":
    main()
