"""
Storage operations for league data.
"""
import logging
from typing import List, Optional, Dict, Any

from database import get_db_cursor
from database.models import League

logger = logging.getLogger(__name__)


class LeagueStorage:
    """Handles storage operations for league data."""
    
    def store_leagues(self, leagues: List[League]) -> int:
        """
        Store leagues in the database.
        
        Args:
            leagues: List of League objects to store
            
        Returns:
            Number of leagues stored
        """
        if not leagues:
            return 0
        
        try:
            with get_db_cursor() as cursor:
                # Prepare data for batch insert
                league_data = []
                for league in leagues:
                    league_data.append((
                        league.id,
                        league.season,
                        league.name,
                        league.country,
                        league.logo,
                        league.flag,
                        league.start_date,
                        league.end_date,
                        league.current
                    ))
                
                # Use ON CONFLICT to handle duplicates
                query = """
                    INSERT INTO leagues (
                        id, season, name, country, logo, flag,
                        start_date, end_date, current
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                    ON CONFLICT (id, season) DO UPDATE SET
                        name = EXCLUDED.name,
                        country = EXCLUDED.country,
                        logo = EXCLUDED.logo,
                        flag = EXCLUDED.flag,
                        start_date = EXCLUDED.start_date,
                        end_date = EXCLUDED.end_date,
                        current = EXCLUDED.current,
                        updated_at = NOW()
                """
                
                cursor.executemany(query, league_data)
                stored_count = cursor.rowcount
                
                logger.info(f"Stored {stored_count} leagues")
                return stored_count
                
        except Exception as e:
            logger.error(f"Failed to store leagues: {e}")
            return 0
    
    def get_league_by_id(self, league_id: int, season: int) -> Optional[Dict[str, Any]]:
        """
        Get league by ID and season.
        
        Args:
            league_id: League ID
            season: Season year
            
        Returns:
            League data or None if not found
        """
        try:
            with get_db_cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM leagues WHERE id = %s AND season = %s", 
                    (league_id, season)
                )
                return cursor.fetchone()
                
        except Exception as e:
            logger.error(f"Failed to get league {league_id}, season {season}: {e}")
            return None
    
    def get_leagues_by_country(self, country: str, season: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get leagues by country.
        
        Args:
            country: Country name
            season: Optional season filter
            
        Returns:
            List of league data
        """
        try:
            with get_db_cursor() as cursor:
                if season:
                    query = "SELECT * FROM leagues WHERE country = %s AND season = %s ORDER BY name"
                    cursor.execute(query, (country, season))
                else:
                    query = "SELECT * FROM leagues WHERE country = %s ORDER BY season DESC, name"
                    cursor.execute(query, (country,))
                
                return cursor.fetchall()
                
        except Exception as e:
            logger.error(f"Failed to get leagues for country {country}: {e}")
            return []
    
    def get_current_leagues(self) -> List[Dict[str, Any]]:
        """
        Get current active leagues.
        
        Returns:
            List of current league data
        """
        try:
            with get_db_cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM leagues WHERE current = TRUE ORDER BY country, name"
                )
                return cursor.fetchall()
                
        except Exception as e:
            logger.error(f"Failed to get current leagues: {e}")
            return []
    
    def get_available_seasons(self, league_id: int) -> List[int]:
        """
        Get available seasons for a league.
        
        Args:
            league_id: League ID
            
        Returns:
            List of available seasons
        """
        try:
            with get_db_cursor() as cursor:
                cursor.execute(
                    "SELECT DISTINCT season FROM leagues WHERE id = %s ORDER BY season DESC",
                    (league_id,)
                )
                return [row['season'] for row in cursor.fetchall()]
                
        except Exception as e:
            logger.error(f"Failed to get seasons for league {league_id}: {e}")
            return []
    
    def search_leagues(self, search_term: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Search leagues by name or country.
        
        Args:
            search_term: Search term
            limit: Maximum number of results
            
        Returns:
            List of league data
        """
        try:
            with get_db_cursor() as cursor:
                query = """
                    SELECT * FROM leagues
                    WHERE name ILIKE %s OR country ILIKE %s
                    ORDER BY current DESC, season DESC, name
                    LIMIT %s
                """
                search_pattern = f"%{search_term}%"
                cursor.execute(query, (search_pattern, search_pattern, limit))
                return cursor.fetchall()
                
        except Exception as e:
            logger.error(f"Failed to search leagues: {e}")
            return []
