"""
Storage operations for match data.
"""
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from database import get_db_cursor
from database.models import Match, UpcomingGame, MatchStatus

logger = logging.getLogger(__name__)


class MatchStorage:
    """Handles storage operations for match data."""
    
    def store_matches(self, matches: List[Match]) -> int:
        """
        Store completed matches in the database.
        
        Args:
            matches: List of Match objects to store
            
        Returns:
            Number of matches stored
        """
        if not matches:
            return 0
        
        try:
            with get_db_cursor() as cursor:
                # Prepare data for batch insert
                match_data = []
                for match in matches:
                    match_data.append((
                        match.id,
                        match.league_id,
                        match.season,
                        match.round,
                        match.date,
                        match.home_team_id,
                        match.away_team_id,
                        match.home_goals,
                        match.away_goals,
                        match.status.value if match.status else 'NS',
                        match.venue_id,
                        match.referee,
                        match.home_shots,
                        match.away_shots,
                        match.home_shots_on_target,
                        match.away_shots_on_target,
                        match.home_possession,
                        match.away_possession,
                        match.home_corners,
                        match.away_corners,
                        match.home_fouls,
                        match.away_fouls,
                        match.home_yellow_cards,
                        match.away_yellow_cards,
                        match.home_red_cards,
                        match.away_red_cards
                    ))
                
                # Use ON CONFLICT to handle duplicates
                query = """
                    INSERT INTO matches (
                        id, league_id, season, round, date, home_team_id, away_team_id,
                        home_goals, away_goals, status, venue_id, referee,
                        home_shots, away_shots, home_shots_on_target, away_shots_on_target,
                        home_possession, away_possession, home_corners, away_corners,
                        home_fouls, away_fouls, home_yellow_cards, away_yellow_cards,
                        home_red_cards, away_red_cards
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                    ON CONFLICT (id) DO UPDATE SET
                        home_goals = EXCLUDED.home_goals,
                        away_goals = EXCLUDED.away_goals,
                        status = EXCLUDED.status,
                        home_shots = EXCLUDED.home_shots,
                        away_shots = EXCLUDED.away_shots,
                        home_shots_on_target = EXCLUDED.home_shots_on_target,
                        away_shots_on_target = EXCLUDED.away_shots_on_target,
                        home_possession = EXCLUDED.home_possession,
                        away_possession = EXCLUDED.away_possession,
                        home_corners = EXCLUDED.home_corners,
                        away_corners = EXCLUDED.away_corners,
                        home_fouls = EXCLUDED.home_fouls,
                        away_fouls = EXCLUDED.away_fouls,
                        home_yellow_cards = EXCLUDED.home_yellow_cards,
                        away_yellow_cards = EXCLUDED.away_yellow_cards,
                        home_red_cards = EXCLUDED.home_red_cards,
                        away_red_cards = EXCLUDED.away_red_cards,
                        updated_at = NOW()
                """
                
                cursor.executemany(query, match_data)
                stored_count = cursor.rowcount
                
                logger.info(f"Stored {stored_count} matches")
                return stored_count
                
        except Exception as e:
            logger.error(f"Failed to store matches: {e}")
            return 0
    
    def store_upcoming_games(self, games: List[UpcomingGame]) -> int:
        """
        Store upcoming games in the database.
        
        Args:
            games: List of UpcomingGame objects to store
            
        Returns:
            Number of games stored
        """
        if not games:
            return 0
        
        try:
            with get_db_cursor() as cursor:
                # Prepare data for batch insert
                game_data = []
                for game in games:
                    game_data.append((
                        game.id,
                        game.league_id,
                        game.season,
                        game.round,
                        game.date,
                        game.home_team_id,
                        game.away_team_id,
                        game.status.value if game.status else 'NS',
                        game.venue_id,
                        game.referee,
                        game.home_goals,
                        game.away_goals
                    ))
                
                # Use ON CONFLICT to handle duplicates
                query = """
                    INSERT INTO upcoming_games (
                        id, league_id, season, round, date, home_team_id, away_team_id,
                        status, venue_id, referee, home_goals, away_goals
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                    ON CONFLICT (id) DO UPDATE SET
                        status = EXCLUDED.status,
                        home_goals = EXCLUDED.home_goals,
                        away_goals = EXCLUDED.away_goals,
                        updated_at = NOW()
                """
                
                cursor.executemany(query, game_data)
                stored_count = cursor.rowcount
                
                logger.info(f"Stored {stored_count} upcoming games")
                return stored_count
                
        except Exception as e:
            logger.error(f"Failed to store upcoming games: {e}")
            return 0
    
    def get_completed_matches(
        self, 
        league_id: Optional[int] = None,
        season: Optional[int] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Get completed matches from database.
        
        Args:
            league_id: Filter by league
            season: Filter by season
            limit: Limit number of results
            
        Returns:
            List of match data
        """
        try:
            with get_db_cursor() as cursor:
                query = """
                    SELECT m.*, 
                           t_home.name as home_team_name,
                           t_away.name as away_team_name,
                           l.name as league_name
                    FROM matches m
                    JOIN teams t_home ON m.home_team_id = t_home.id
                    JOIN teams t_away ON m.away_team_id = t_away.id
                    JOIN leagues l ON m.league_id = l.id AND m.season = l.season
                    WHERE 1=1
                """
                params = []
                
                if league_id:
                    query += " AND m.league_id = %s"
                    params.append(league_id)
                
                if season:
                    query += " AND m.season = %s"
                    params.append(season)
                
                query += " ORDER BY m.date DESC"
                
                if limit:
                    query += " LIMIT %s"
                    params.append(limit)
                
                cursor.execute(query, params)
                return cursor.fetchall()
                
        except Exception as e:
            logger.error(f"Failed to get completed matches: {e}")
            return []
    
    def get_upcoming_games(
        self, 
        league_id: Optional[int] = None,
        season: Optional[int] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Get upcoming games from database.
        
        Args:
            league_id: Filter by league
            season: Filter by season
            limit: Limit number of results
            
        Returns:
            List of upcoming game data
        """
        try:
            with get_db_cursor() as cursor:
                query = """
                    SELECT u.*, 
                           t_home.name as home_team_name,
                           t_away.name as away_team_name,
                           l.name as league_name
                    FROM upcoming_games u
                    JOIN teams t_home ON u.home_team_id = t_home.id
                    JOIN teams t_away ON u.away_team_id = t_away.id
                    JOIN leagues l ON u.league_id = l.id AND u.season = l.season
                    WHERE 1=1
                """
                params = []
                
                if league_id:
                    query += " AND u.league_id = %s"
                    params.append(league_id)
                
                if season:
                    query += " AND u.season = %s"
                    params.append(season)
                
                query += " ORDER BY u.date ASC"
                
                if limit:
                    query += " LIMIT %s"
                    params.append(limit)
                
                cursor.execute(query, params)
                return cursor.fetchall()
                
        except Exception as e:
            logger.error(f"Failed to get upcoming games: {e}")
            return []
    
    def move_completed_games_to_matches(self) -> int:
        """
        Move completed games from upcoming_games to matches table.
        
        Returns:
            Number of games moved
        """
        try:
            with get_db_cursor() as cursor:
                # Find completed games that can be moved
                cursor.execute("""
                    SELECT * FROM upcoming_games
                    WHERE status IN ('FT', 'AET', 'PEN')
                    AND home_goals IS NOT NULL
                    AND away_goals IS NOT NULL
                """)
                
                completed_games = cursor.fetchall()
                
                if not completed_games:
                    logger.info("No completed games to move")
                    return 0
                
                moved_count = 0
                
                for game in completed_games:
                    try:
                        # Insert into matches table
                        cursor.execute("""
                            INSERT INTO matches (
                                id, league_id, season, round, date, home_team_id, away_team_id,
                                home_goals, away_goals, status, venue_id, referee
                            ) VALUES (
                                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                            )
                            ON CONFLICT (id) DO UPDATE SET
                                home_goals = EXCLUDED.home_goals,
                                away_goals = EXCLUDED.away_goals,
                                status = EXCLUDED.status,
                                updated_at = NOW()
                        """, (
                            game['id'], game['league_id'], game['season'], game['round'],
                            game['date'], game['home_team_id'], game['away_team_id'],
                            game['home_goals'], game['away_goals'], game['status'],
                            game['venue_id'], game['referee']
                        ))
                        
                        # Remove from upcoming_games table
                        cursor.execute("DELETE FROM upcoming_games WHERE id = %s", (game['id'],))
                        
                        moved_count += 1
                        
                    except Exception as e:
                        logger.error(f"Failed to move game {game['id']}: {e}")
                        continue
                
                logger.info(f"Moved {moved_count} completed games to matches table")
                return moved_count
                
        except Exception as e:
            logger.error(f"Failed to move completed games: {e}")
            return 0
