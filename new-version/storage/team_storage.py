"""
Storage operations for team data.
"""
import logging
from typing import List, Optional, Dict, Any

from database import get_db_cursor
from database.models import Team

logger = logging.getLogger(__name__)


class TeamStorage:
    """Handles storage operations for team data."""
    
    def store_teams(self, teams: List[Team]) -> int:
        """
        Store teams in the database.
        
        Args:
            teams: List of Team objects to store
            
        Returns:
            Number of teams stored
        """
        if not teams:
            return 0
        
        try:
            with get_db_cursor() as cursor:
                # Prepare data for batch insert
                team_data = []
                for team in teams:
                    team_data.append((
                        team.id,
                        team.name,
                        team.code,
                        team.country,
                        team.founded,
                        team.logo,
                        team.venue_id,
                        team.venue_name,
                        team.venue_capacity
                    ))
                
                # Use ON CONFLICT to handle duplicates
                query = """
                    INSERT INTO teams (
                        id, name, code, country, founded, logo,
                        venue_id, venue_name, venue_capacity
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                    ON CONFLICT (id) DO UPDATE SET
                        name = EXCLUDED.name,
                        code = EXCLUDED.code,
                        country = EXCLUDED.country,
                        founded = EXCLUDED.founded,
                        logo = EXCLUDED.logo,
                        venue_id = EXCLUDED.venue_id,
                        venue_name = EXCLUDED.venue_name,
                        venue_capacity = EXCLUDED.venue_capacity,
                        updated_at = NOW()
                """
                
                cursor.executemany(query, team_data)
                stored_count = cursor.rowcount
                
                logger.info(f"Stored {stored_count} teams")
                return stored_count
                
        except Exception as e:
            logger.error(f"Failed to store teams: {e}")
            return 0
    
    def get_team_by_id(self, team_id: int) -> Optional[Dict[str, Any]]:
        """
        Get team by ID.
        
        Args:
            team_id: Team ID
            
        Returns:
            Team data or None if not found
        """
        try:
            with get_db_cursor() as cursor:
                cursor.execute("SELECT * FROM teams WHERE id = %s", (team_id,))
                return cursor.fetchone()
                
        except Exception as e:
            logger.error(f"Failed to get team {team_id}: {e}")
            return None
    
    def get_teams_by_league(self, league_id: int, season: int) -> List[Dict[str, Any]]:
        """
        Get teams that played in a specific league and season.
        
        Args:
            league_id: League ID
            season: Season year
            
        Returns:
            List of team data
        """
        try:
            with get_db_cursor() as cursor:
                query = """
                    SELECT DISTINCT t.*
                    FROM teams t
                    WHERE t.id IN (
                        SELECT DISTINCT home_team_id FROM matches 
                        WHERE league_id = %s AND season = %s
                        UNION
                        SELECT DISTINCT away_team_id FROM matches 
                        WHERE league_id = %s AND season = %s
                        UNION
                        SELECT DISTINCT home_team_id FROM upcoming_games 
                        WHERE league_id = %s AND season = %s
                        UNION
                        SELECT DISTINCT away_team_id FROM upcoming_games 
                        WHERE league_id = %s AND season = %s
                    )
                    ORDER BY t.name
                """
                cursor.execute(query, (league_id, season, league_id, season, league_id, season, league_id, season))
                return cursor.fetchall()
                
        except Exception as e:
            logger.error(f"Failed to get teams for league {league_id}, season {season}: {e}")
            return []
    
    def search_teams(self, search_term: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Search teams by name.
        
        Args:
            search_term: Search term
            limit: Maximum number of results
            
        Returns:
            List of team data
        """
        try:
            with get_db_cursor() as cursor:
                query = """
                    SELECT * FROM teams
                    WHERE name ILIKE %s OR code ILIKE %s
                    ORDER BY name
                    LIMIT %s
                """
                search_pattern = f"%{search_term}%"
                cursor.execute(query, (search_pattern, search_pattern, limit))
                return cursor.fetchall()
                
        except Exception as e:
            logger.error(f"Failed to search teams: {e}")
            return []
