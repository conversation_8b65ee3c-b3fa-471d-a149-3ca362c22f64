#!/usr/bin/env python3
"""
Test script to demonstrate the new football prediction system features.
"""
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from utils.logger import setup_logging
from database.connection import initialize_connection_pool, test_connection, close_connection_pool
from database.migrations import setup_database
from ml import ModelManager, FootballPredictor, AdaptiveLearningSystem, run_adaptive_learning


def setup_test_environment():
    """Set up the test environment."""
    print("🚀 Setting up test environment...")
    
    # Setup logging
    setup_logging()
    
    # Initialize database
    initialize_connection_pool()
    if not test_connection():
        print("❌ Database connection failed!")
        return False
    
    # Setup database tables
    if not setup_database():
        print("❌ Database setup failed!")
        return False
    
    print("✅ Test environment ready!")
    return True


def test_model_management():
    """Test model management features."""
    print("\n" + "=" * 60)
    print("🤖 TESTING MODEL MANAGEMENT")
    print("=" * 60)
    
    model_manager = ModelManager()
    
    # Check if model exists
    print("\n1. Checking for existing models...")
    existing_model = model_manager.check_existing_model()
    print(f"   Existing model found: {existing_model}")
    
    # List all models
    print("\n2. Listing all models...")
    models = model_manager.list_models()
    print(f"   Found {len(models)} models in database")
    for model in models:
        status = "🟢 Active" if model['is_active'] else "⚪ Inactive"
        print(f"   • {model['name']} v{model['version']} ({status})")
    
    # Get model info
    if existing_model:
        print("\n3. Getting active model information...")
        model_info = model_manager.get_model_info()
        if model_info:
            print(f"   • Name: {model_info['name']}")
            print(f"   • Version: {model_info['version']}")
            print(f"   • Type: {model_info['model_type']}")
            print(f"   • Accuracy: {model_info['validation_accuracy']:.3f}")
        
        # Test model loading
        print("\n4. Testing model loading...")
        if model_manager.load_model():
            print("   ✅ Model loaded successfully!")
            print(f"   • Features: {len(model_manager.feature_columns)} columns")
            print(f"   • Models: {list(model_manager.models.keys()) if model_manager.models else 'None'}")
        else:
            print("   ❌ Failed to load model")
    else:
        print("\n3. No active model to test loading")
    
    # Check training data availability
    print("\n5. Checking training data availability...")
    training_data = model_manager.get_training_data()
    if training_data is not None:
        print(f"   ✅ Training data available: {len(training_data)} matches")
        print(f"   • Columns: {list(training_data.columns)}")
    else:
        print("   ❌ Insufficient training data (need at least 50 matches)")


def test_prediction_system():
    """Test prediction system features."""
    print("\n" + "=" * 60)
    print("🎯 TESTING PREDICTION SYSTEM")
    print("=" * 60)
    
    predictor = FootballPredictor()
    
    # Test model loading
    print("\n1. Loading prediction model...")
    if predictor.load_model():
        print("   ✅ Model loaded successfully!")
    else:
        print("   ❌ No model available for predictions")
        return
    
    # Get upcoming games without predictions
    print("\n2. Checking upcoming games without predictions...")
    games = predictor.get_upcoming_games_without_predictions()
    print(f"   Found {len(games)} upcoming games without predictions")
    
    if games:
        # Show first few games
        for i, game in enumerate(games[:3], 1):
            print(f"   {i}. {game['home_team_name']} vs {game['away_team_name']}")
            print(f"      • League: {game['league_name']}")
            print(f"      • Date: {game['date']}")
        
        # Test single prediction
        print(f"\n3. Testing single prediction...")
        test_game = games[0]
        prediction = predictor.predict_match(test_game)
        
        if prediction:
            outcome_names = {1: "Home Win", 0: "Draw", 2: "Away Win"}
            predicted_outcome = outcome_names.get(prediction['predicted_outcome'].value, 'Unknown')
            
            print("   ✅ Prediction completed!")
            print(f"   • Match: {test_game['home_team_name']} vs {test_game['away_team_name']}")
            print(f"   • Outcome: {predicted_outcome}")
            print(f"   • Score: {prediction['predicted_home_goals']:.1f} - {prediction['predicted_away_goals']:.1f}")
            print(f"   • Confidence: H:{prediction['confidence_home_win']:.2f} D:{prediction['confidence_draw']:.2f} A:{prediction['confidence_away_win']:.2f}")
        else:
            print("   ❌ Failed to make prediction")
    else:
        print("   No upcoming games available for testing")


def test_adaptive_learning():
    """Test adaptive learning system."""
    print("\n" + "=" * 60)
    print("🧠 TESTING ADAPTIVE LEARNING SYSTEM")
    print("=" * 60)
    
    learning_system = AdaptiveLearningSystem()
    
    # Test model loading
    print("\n1. Loading model for adaptive learning...")
    if learning_system.load_model():
        print("   ✅ Model loaded successfully!")
    else:
        print("   ❌ No model available for adaptive learning")
        return
    
    # Test performance analysis
    print("\n2. Analyzing prediction performance...")
    analysis = learning_system.analyze_prediction_performance(days_back=30)
    
    if analysis:
        print("   ✅ Performance analysis completed!")
        print(f"   • Total Predictions: {analysis['total_predictions']}")
        print(f"   • Outcome Accuracy: {analysis['outcome_accuracy']:.3f}")
        print(f"   • Goals Accuracy: {analysis['goals_accuracy']:.3f}")
        print(f"   • High Confidence Predictions: {analysis['high_confidence_count']}")
        print(f"   • High Confidence Accuracy: {analysis['high_confidence_accuracy']:.3f}")
        print(f"   • Confidence Calibration: {analysis['confidence_calibration']:.3f}")
    else:
        print("   ❌ Insufficient data for performance analysis")
    
    # Test intelligent sample selection
    print("\n3. Testing intelligent sample selection...")
    samples = learning_system.intelligent_sample_selection(max_samples=10)
    print(f"   Selected {len(samples)} high-value samples for training")
    
    if samples:
        print("   Sample details:")
        for i, sample in enumerate(samples[:3], 1):
            print(f"   {i}. Match ID: {sample['match_id']}")
            print(f"      • Prediction Date: {sample['prediction_date']}")
            print(f"      • Predicted: {sample['predicted_outcome']} | Actual: {sample['actual_outcome']}")
    
    # Test adaptive learning cycle
    print("\n4. Testing adaptive learning cycle...")
    result = run_adaptive_learning()
    
    if result['success']:
        if result.get('learned', False):
            print("   ✅ Adaptive learning completed: Model improved!")
            retrain_results = result.get('retrain_results', {})
            print(f"   • Samples used: {retrain_results.get('samples_used', 'N/A')}")
            print(f"   • Reasons: {', '.join(result.get('reasons', []))}")
        else:
            print("   ✅ Adaptive learning completed: No improvement needed")
            print(f"   • Message: {result.get('message', 'Model performance is satisfactory')}")
    else:
        print(f"   ❌ Adaptive learning failed: {result['error']}")


def test_database_features():
    """Test database features."""
    print("\n" + "=" * 60)
    print("🗄️ TESTING DATABASE FEATURES")
    print("=" * 60)
    
    from database import get_db_cursor
    
    # Test new tables
    print("\n1. Checking new database tables...")
    
    tables_to_check = [
        'models',
        'learning_history',
        'predictions',
        'model_performance'
    ]
    
    with get_db_cursor() as cursor:
        for table in tables_to_check:
            cursor.execute(f"""
                SELECT COUNT(*) as count 
                FROM information_schema.tables 
                WHERE table_name = %s
            """, (table,))
            result = cursor.fetchone()
            
            if result['count'] > 0:
                cursor.execute(f"SELECT COUNT(*) as row_count FROM {table}")
                row_count = cursor.fetchone()['row_count']
                print(f"   ✅ Table '{table}' exists with {row_count} rows")
            else:
                print(f"   ❌ Table '{table}' not found")


def main():
    """Main test function."""
    print("🧪 FOOTBALL PREDICTION SYSTEM - FEATURE TESTING")
    print("=" * 60)
    
    try:
        # Setup test environment
        if not setup_test_environment():
            print("❌ Failed to setup test environment")
            return
        
        # Run tests
        test_database_features()
        test_model_management()
        test_prediction_system()
        test_adaptive_learning()
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS COMPLETED!")
        print("=" * 60)
        print()
        print("🎯 Summary of implemented features:")
        print("  ✅ Enhanced database schema with ML tables")
        print("  ✅ Model Management system")
        print("  ✅ Prediction system for upcoming games")
        print("  ✅ Adaptive Learning system")
        print("  ✅ Enhanced data management with prediction updates")
        print("  ✅ Comprehensive UI menus")
        print()
        print("🚀 Ready for production use!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        close_connection_pool()


if __name__ == "__main__":
    main()
