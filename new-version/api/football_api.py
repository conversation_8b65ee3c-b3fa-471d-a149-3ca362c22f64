"""
Football API client for fetching match and team data.
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, date

from .client import ApiClient
from config import config

logger = logging.getLogger(__name__)


class FootballApiClient(ApiClient):
    """Client for Football API (API-Sports)."""

    def __init__(self):
        """Initialize Football API client."""
        headers = {
            'x-apisports-key': config.api.football_api_key
        }
        super().__init__(config.api.base_url, headers)

    def test_connection(self) -> bool:
        """
        Test API connection by fetching timezone data.

        Returns:
            True if connection successful, False otherwise
        """
        try:
            response = self.get('timezone')
            return 'response' in response and len(response['response']) > 0
        except Exception as e:
            logger.error(f"API connection test failed: {e}")
            return False

    def get_countries(self) -> List[Dict[str, Any]]:
        """
        Get all available countries.

        Returns:
            List of country data
        """
        try:
            response = self.get('countries')
            return response.get('response', [])
        except Exception as e:
            logger.error(f"Failed to fetch countries: {e}")
            return []

    def get_seasons(self) -> List[int]:
        """
        Get all available seasons.

        Returns:
            List of season years
        """
        try:
            response = self.get('seasons')
            return response.get('response', [])
        except Exception as e:
            logger.error(f"Failed to fetch seasons: {e}")
            return []

    def get_leagues(self, country: Optional[str] = None, season: Optional[int] = None, league_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get leagues data.

        Args:
            country: Filter by country
            season: Filter by season
            league_id: Filter by specific league ID

        Returns:
            List of league data
        """
        params = {}
        if league_id:
            params['id'] = league_id
        if country:
            params['country'] = country
        if season:
            params['season'] = season

        try:
            response = self.get('leagues', params=params)
            return response.get('response', [])
        except Exception as e:
            logger.error(f"Failed to fetch leagues: {e}")
            return []

    def get_teams(self, league_id: int, season: int) -> List[Dict[str, Any]]:
        """
        Get teams for a specific league and season.

        Args:
            league_id: League ID
            season: Season year

        Returns:
            List of team data
        """
        params = {
            'league': league_id,
            'season': season
        }

        try:
            response = self.get('teams', params=params)
            return response.get('response', [])
        except Exception as e:
            logger.error(f"Failed to fetch teams for league {league_id}, season {season}: {e}")
            return []

    def get_fixtures(
        self,
        league_id: Optional[int] = None,
        season: Optional[int] = None,
        team_id: Optional[int] = None,
        date_from: Optional[date] = None,
        date_to: Optional[date] = None,
        fixture_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Get fixtures/matches data.

        Args:
            league_id: Filter by league
            season: Filter by season
            team_id: Filter by team
            date_from: Start date filter
            date_to: End date filter
            fixture_id: Specific fixture ID

        Returns:
            List of fixture data
        """
        params = {}

        if fixture_id:
            params['id'] = fixture_id
        else:
            if league_id:
                params['league'] = league_id
            if season:
                params['season'] = season
            if team_id:
                params['team'] = team_id
            if date_from:
                params['from'] = date_from.strftime('%Y-%m-%d')
            if date_to:
                params['to'] = date_to.strftime('%Y-%m-%d')

        try:
            response = self.get('fixtures', params=params)
            return response.get('response', [])
        except Exception as e:
            logger.error(f"Failed to fetch fixtures: {e}")
            return []

    def get_fixture_statistics(self, fixture_id: int, team_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get detailed statistics for a specific fixture.

        Args:
            fixture_id: Fixture ID
            team_id: Optional team ID to get statistics for specific team

        Returns:
            List of statistics data
        """
        params = {'fixture': fixture_id}

        if team_id:
            params['team'] = team_id

        try:
            response = self.get('fixtures/statistics', params=params)
            result = response.get('response', [])

            # Debug logging for empty responses
            if not result:
                logger.debug(f"🔍 Empty statistics response for fixture {fixture_id}, team {team_id}")
                logger.debug(f"   Full API response: {response}")
            elif len(result) == 1 and not result[0].get('statistics'):
                logger.debug(f"🔍 Statistics response has no 'statistics' field for fixture {fixture_id}, team {team_id}")
                logger.debug(f"   Response structure: {list(result[0].keys()) if result[0] else 'Empty'}")

            return result
        except Exception as e:
            logger.error(f"Failed to fetch statistics for fixture {fixture_id}, team {team_id}: {e}")
            return []

    def get_head_to_head(self, team1_id: int, team2_id: int, last: int = 10) -> List[Dict[str, Any]]:
        """
        Get head-to-head matches between two teams.

        Args:
            team1_id: First team ID
            team2_id: Second team ID
            last: Number of recent matches to fetch

        Returns:
            List of head-to-head matches
        """
        params = {
            'h2h': f"{team1_id}-{team2_id}",
            'last': last
        }

        try:
            response = self.get('fixtures/headtohead', params=params)
            return response.get('response', [])
        except Exception as e:
            logger.error(f"Failed to fetch H2H for teams {team1_id} vs {team2_id}: {e}")
            return []

    def get_team_statistics(self, team_id: int, league_id: int, season: int) -> Dict[str, Any]:
        """
        Get team statistics for a specific league and season.

        Args:
            team_id: Team ID
            league_id: League ID
            season: Season year

        Returns:
            Team statistics data
        """
        params = {
            'team': team_id,
            'league': league_id,
            'season': season
        }

        try:
            response = self.get('teams/statistics', params=params)
            return response.get('response', {})
        except Exception as e:
            logger.error(f"Failed to fetch statistics for team {team_id}: {e}")
            return {}

    def get_standings(self, league_id: int, season: int) -> List[Dict[str, Any]]:
        """
        Get league standings.

        Args:
            league_id: League ID
            season: Season year

        Returns:
            List of standings data
        """
        params = {
            'league': league_id,
            'season': season
        }

        try:
            response = self.get('standings', params=params)
            return response.get('response', [])
        except Exception as e:
            logger.error(f"Failed to fetch standings for league {league_id}, season {season}: {e}")
            return []
